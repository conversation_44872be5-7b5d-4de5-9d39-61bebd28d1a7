import Link from "next/link";
import { Headphones, Music, Download } from "lucide-react";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center p-8">
      <div className="max-w-4xl mx-auto text-center">
        <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-12 shadow-2xl">
          <div className="flex justify-center mb-8">
            <div className="bg-gradient-to-r from-purple-500 to-cyan-500 p-6 rounded-3xl shadow-lg animate-pulse">
              <Headphones className="w-16 h-16 text-white" />
            </div>
          </div>

          <h1 className="text-5xl sm:text-7xl font-bold bg-gradient-to-r from-purple-400 to-cyan-400 bg-clip-text text-transparent mb-6">
            SoundWave Pro
          </h1>

          <p className="text-xl text-gray-300 mb-12 max-w-2xl mx-auto">
            Welcome to the next generation of audio conversion technology.
            Transform your sound experience with our cutting-edge tools.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
            <div className="bg-black/20 backdrop-blur-sm p-8 rounded-2xl border border-white/10 hover:border-purple-500/50 transition-all duration-300 group">
              <Music className="w-12 h-12 text-purple-400 mb-4 mx-auto group-hover:scale-110 transition-transform" />
              <h3 className="text-xl font-bold text-white mb-3">SoundCloud Downloader</h3>
              <p className="text-gray-400 mb-6">Convert and download your favorite SoundCloud tracks with premium quality</p>
              <Link
                href="/soundcloud"
                className="inline-flex items-center bg-gradient-to-r from-purple-500 to-cyan-500 text-white px-6 py-3 rounded-xl font-semibold hover:shadow-lg transition-all duration-300 transform hover:scale-105"
              >
                <Download className="w-5 h-5 mr-2" />
                Try Now
              </Link>
            </div>

            <div className="bg-black/20 backdrop-blur-sm p-8 rounded-2xl border border-white/10 hover:border-cyan-500/50 transition-all duration-300 group opacity-50">
              <div className="w-12 h-12 bg-gray-600 rounded-lg mb-4 mx-auto flex items-center justify-center">
                <span className="text-gray-400 text-xs">Soon</span>
              </div>
              <h3 className="text-xl font-bold text-gray-400 mb-3">More Tools Coming</h3>
              <p className="text-gray-500 mb-6">Additional audio conversion tools will be available soon</p>
              <button
                disabled
                className="inline-flex items-center bg-gray-600 text-gray-400 px-6 py-3 rounded-xl font-semibold cursor-not-allowed"
              >
                Coming Soon
              </button>
            </div>
          </div>

          <div className="text-center">
            <p className="text-gray-400 text-sm">
              Built with Next.js, React, and Tailwind CSS
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
