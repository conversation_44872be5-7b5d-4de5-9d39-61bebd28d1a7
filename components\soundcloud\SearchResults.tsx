'use client';

import React from 'react';
import { Stars } from 'lucide-react';
import { Track } from '@/types/soundcloud';
import { useTheme } from './ThemeProvider';
import { Card } from '@/components/ui/Card';
import { TrackCard } from './TrackCard';

interface SearchResultsProps {
  tracks: Track[];
  onDownload: (track: Track) => void;
}

export const SearchResults: React.FC<SearchResultsProps> = ({ tracks, onDownload }) => {
  const { theme, isClient } = useTheme();

  if (tracks.length === 0) {
    return null;
  }

  return (
    <Card padding="lg" className="mb-12">
      <h2 className={`text-2xl sm:text-3xl font-bold ${theme.text} mb-8 flex items-center`}>
        <Stars className="w-8 h-8 mr-3 text-yellow-400" />
        Discovered Tracks
      </h2>

      <div className="grid gap-6">
        {tracks.map((track, index) => (
          <TrackCard
            key={track.id}
            track={track}
            index={isClient ? index : 0} // Prevent hydration mismatch
            onDownload={onDownload}
          />
        ))}
      </div>
    </Card>
  );
};
