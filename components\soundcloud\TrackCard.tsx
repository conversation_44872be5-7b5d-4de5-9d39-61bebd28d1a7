'use client';

import React from 'react';
import { Play, Heart, Share2, Download, Clock, User } from 'lucide-react';
import { Track } from '@/types/soundcloud';
import { useTheme } from './ThemeProvider';
import { Button } from '@/components/ui/Button';

interface TrackCardProps {
  track: Track;
  index: number;
  onDownload: (track: Track) => void;
}

export const TrackCard: React.FC<TrackCardProps> = ({ track, index, onDownload }) => {
  const { theme, isClient } = useTheme();

  return (
    <div
      className={`flex flex-col sm:flex-row items-center p-6 bg-black/20 backdrop-blur-sm rounded-2xl hover:bg-black/30 transition-all duration-300 transform hover:scale-105`}
      style={isClient ? { animationDelay: `${index * 0.1}s` } : {}}
    >
      {/* Artwork */}
      <div className="relative mb-4 sm:mb-0 sm:mr-6 group">
        <img 
          src={track.artwork} 
          alt={track.title}
          className="w-20 h-20 sm:w-24 sm:h-24 rounded-2xl object-cover shadow-lg"
        />
        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-0 group-hover:bg-opacity-60 rounded-2xl transition-all cursor-pointer">
          <Play className="w-8 h-8 text-white opacity-0 group-hover:opacity-100 transition-opacity transform group-hover:scale-110" />
        </div>
        <div className={`absolute -top-2 -right-2 bg-gradient-to-r ${theme.accent} w-6 h-6 rounded-full animate-pulse`}></div>
      </div>

      {/* Track Info */}
      <div className="flex-1 text-center sm:text-left mb-4 sm:mb-0">
        <h3 className={`font-bold ${theme.text} text-lg sm:text-xl mb-2`}>{track.title}</h3>
        <div className={`flex flex-wrap items-center justify-center sm:justify-start ${theme.textSecondary} text-sm gap-4`}>
          <span className="flex items-center">
            <User className="w-4 h-4 mr-1" />
            {track.artist}
          </span>
          <span className="flex items-center">
            <Clock className="w-4 h-4 mr-1" />
            {track.duration}
          </span>
          <span className="flex items-center">
            <Play className="w-4 h-4 mr-1" />
            {track.plays}
          </span>
          <span className="flex items-center">
            <Heart className="w-4 h-4 mr-1" />
            {track.likes}
          </span>
        </div>
      </div>

      {/* Actions */}
      <div className="flex items-center space-x-3">
        <button className={`p-3 ${theme.textSecondary} hover:text-red-400 transition-colors transform hover:scale-110`}>
          <Heart className="w-6 h-6" />
        </button>
        <button className={`p-3 ${theme.textSecondary} hover:text-blue-400 transition-colors transform hover:scale-110`}>
          <Share2 className="w-6 h-6" />
        </button>
        <Button
          onClick={() => onDownload(track)}
          gradient={theme.accent}
          size="md"
          className="font-semibold"
        >
          <Download className="w-5 h-5 mr-2" />
          Download
        </Button>
      </div>
    </div>
  );
};
