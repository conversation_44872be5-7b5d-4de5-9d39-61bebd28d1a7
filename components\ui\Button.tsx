'use client';

import React, { ButtonHTMLAttributes, ReactNode } from 'react';
import { cn } from '@/lib/utils';

interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  children: ReactNode;
  isLoading?: boolean;
  gradient?: string;
}

export const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  children,
  isLoading = false,
  gradient = 'from-purple-500 to-cyan-500',
  className,
  disabled,
  ...props
}) => {
  const baseClasses = 'font-semibold rounded-xl transition-all duration-300 transform hover:scale-105 disabled:scale-100 flex items-center justify-center relative overflow-hidden group';
  
  const variants = {
    primary: `bg-gradient-to-r ${gradient} hover:shadow-2xl text-white shadow-lg`,
    secondary: 'bg-black/20 backdrop-blur-sm hover:bg-black/30 border border-white/10',
    ghost: 'hover:bg-white/10 transition-colors'
  };

  const sizes = {
    sm: 'px-4 py-2 text-sm',
    md: 'px-6 py-3 text-base',
    lg: 'px-8 py-6 text-xl'
  };

  return (
    <button
      className={cn(
        baseClasses,
        variants[variant],
        sizes[size],
        disabled || isLoading ? 'opacity-50 cursor-not-allowed' : '',
        className
      )}
      disabled={disabled || isLoading}
      {...props}
    >
      {variant === 'primary' && (
        <div className="absolute inset-0 bg-white/20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
      )}
      <span className="relative z-10 flex items-center">
        {isLoading && (
          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
        )}
        {children}
      </span>
    </button>
  );
};
