'use client';

import React, { createContext, useContext, useState, ReactNode, useEffect } from 'react';
import { Theme, ThemeType } from '@/types/soundcloud';
import { getThemes } from '@/lib/themes';

interface ThemeContextType {
  theme: Theme;
  currentTheme: ThemeType;
  isDark: boolean;
  setCurrentTheme: (theme: ThemeType) => void;
  setIsDark: (isDark: boolean) => void;
  isClient: boolean;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [currentTheme, setCurrentTheme] = useState<ThemeType>('cyberpunk');
  const [isDark, setIsDark] = useState(true);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const themes = getThemes(isDark);
  const theme = themes[currentTheme];

  const value = {
    theme,
    currentTheme,
    isDark,
    setCurrentTheme,
    setIsDark,
    isClient,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};
