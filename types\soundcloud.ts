export interface Track {
  id: number;
  title: string;
  artist: string;
  duration: string;
  plays: string;
  likes: string;
  artwork: string;
}

export interface Theme {
  name: string;
  bg: string;
  cardBg: string;
  accent: string;
  text: string;
  textSecondary: string;
}

export interface ThemeConfig {
  [key: string]: Theme;
}

export interface Feature {
  icon: any;
  title: string;
  description: string;
  gradient: string;
}

export interface HowItWorksStep {
  step: string;
  title: string;
  description: string;
  icon: any;
}

export type TabType = 'single' | 'playlist';
export type ThemeType = 'cyberpunk' | 'neon' | 'ocean' | 'sunset';
