'use client';

import React, { useState } from 'react';
import { Bug, Play, X } from 'lucide-react';
import { useTheme } from './ThemeProvider';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

interface DebugPanelProps {
  isVisible: boolean;
  onToggle: () => void;
}

export const DebugPanel: React.FC<DebugPanelProps> = ({ isVisible, onToggle }) => {
  const [testResult, setTestResult] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const { theme } = useTheme();

  const testAPI = async () => {
    setIsLoading(true);
    setTestResult('Testing API...');

    try {
      // Test API endpoint
      const response = await fetch('/api/download', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          url: 'https://soundcloud.com/test/track',
          quality: '128'
        }),
      });

      const data = await response.json();
      setTestResult(`API Test Result:\nStatus: ${response.status}\nResponse: ${JSON.stringify(data, null, 2)}`);
    } catch (error) {
      setTestResult(`API Test Error: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  if (!isVisible) {
    return (
      <div className="fixed bottom-6 left-6 z-50">
        <Button
          onClick={onToggle}
          variant="secondary"
          size="sm"
          className="rounded-full shadow-lg"
        >
          <Bug className="w-4 h-4 mr-2" />
          Debug
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl max-h-[80vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h2 className={`text-2xl font-bold ${theme.text} flex items-center`}>
            <Bug className="w-6 h-6 mr-3" />
            Debug Panel
          </h2>
          <Button
            onClick={onToggle}
            variant="ghost"
            size="sm"
            className="p-2"
          >
            <X className="w-5 h-5" />
          </Button>
        </div>

        {/* API Test */}
        <div className="space-y-4">
          <div>
            <h3 className={`text-lg font-semibold ${theme.text} mb-3`}>API Test</h3>
            <Button
              onClick={testAPI}
              isLoading={isLoading}
              gradient={theme.accent}
              size="md"
              className="mb-4"
            >
              <Play className="w-4 h-4 mr-2" />
              Test Download API
            </Button>
          </div>

          {/* Test Result */}
          {testResult && (
            <div className="bg-black/20 rounded-xl p-4">
              <h4 className={`${theme.text} font-medium mb-2`}>Test Result:</h4>
              <pre className={`${theme.textSecondary} text-xs whitespace-pre-wrap overflow-auto max-h-60`}>
                {testResult}
              </pre>
            </div>
          )}

          {/* System Info */}
          <div className="bg-black/20 rounded-xl p-4">
            <h4 className={`${theme.text} font-medium mb-2`}>System Info:</h4>
            <div className={`${theme.textSecondary} text-sm space-y-1`}>
              <p>User Agent: {navigator.userAgent}</p>
              <p>URL: {window.location.href}</p>
              <p>Timestamp: {new Date().toISOString()}</p>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="space-y-2">
            <h4 className={`${theme.text} font-medium`}>Quick Actions:</h4>
            <div className="flex space-x-2">
              <Button
                onClick={() => console.log('Console test')}
                variant="secondary"
                size="sm"
              >
                Console Test
              </Button>
              <Button
                onClick={() => localStorage.clear()}
                variant="secondary"
                size="sm"
              >
                Clear Storage
              </Button>
              <Button
                onClick={() => window.location.reload()}
                variant="secondary"
                size="sm"
              >
                Reload Page
              </Button>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};
