// Download service for handling SoundCloud downloads
export interface DownloadJob {
  id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  trackInfo?: TrackInfo;
  downloadUrl?: string;
  error?: string;
}

export interface TrackInfo {
  id: string;
  title: string;
  artist: string;
  duration: number;
  artwork_url: string;
  stream_url?: string;
  download_url?: string;
  downloadable: boolean;
}

export interface DownloadRequest {
  url: string;
  quality?: '128' | '256' | 'best';
}

export interface DownloadResponse {
  success: boolean;
  jobId?: string;
  message?: string;
  error?: string;
  details?: any;
}

export interface JobStatusResponse {
  success: boolean;
  job?: DownloadJob;
  error?: string;
}

class DownloadService {
  private baseUrl = '/api';

  async startDownload(request: DownloadRequest): Promise<DownloadResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/download`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      const data = await response.json();
      return data;
    } catch (error) {
      return {
        success: false,
        error: 'Network error occurred',
      };
    }
  }

  async getJobStatus(jobId: string): Promise<JobStatusResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/download?jobId=${jobId}`);
      const data = await response.json();
      return data;
    } catch (error) {
      return {
        success: false,
        error: 'Failed to fetch job status',
      };
    }
  }

  async pollJobStatus(
    jobId: string,
    onUpdate: (job: DownloadJob) => void,
    interval: number = 1000
  ): Promise<DownloadJob> {
    return new Promise((resolve, reject) => {
      const poll = async () => {
        try {
          const response = await this.getJobStatus(jobId);
          
          if (!response.success || !response.job) {
            reject(new Error(response.error || 'Job not found'));
            return;
          }

          const job = response.job;
          onUpdate(job);

          if (job.status === 'completed' || job.status === 'failed') {
            resolve(job);
          } else {
            setTimeout(poll, interval);
          }
        } catch (error) {
          reject(error);
        }
      };

      poll();
    });
  }

  validateSoundCloudUrl(url: string): boolean {
    const soundcloudRegex = /^https?:\/\/(www\.)?soundcloud\.com\/[a-zA-Z0-9-_]+\/[a-zA-Z0-9-_]+/;
    return soundcloudRegex.test(url);
  }

  formatDuration(milliseconds: number): string {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  formatFileSize(bytes: number): string {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }
}

export const downloadService = new DownloadService();
