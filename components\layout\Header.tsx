'use client';

import React from 'react';
import { Headphones, Sun, Moon } from 'lucide-react';
import { useTheme } from '@/components/soundcloud/ThemeProvider';
import { getThemes } from '@/lib/themes';
import { ThemeType } from '@/types/soundcloud';

export const Header: React.FC = () => {
  const { theme, currentTheme, isDark, setCurrentTheme, setIsDark } = useTheme();
  const themes = getThemes(isDark);

  return (
    <header className={`${theme.cardBg} sticky top-0 z-50 transition-all duration-500`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
          <div className="flex items-center space-x-4">
            <div className={`bg-gradient-to-r ${theme.accent} p-3 rounded-2xl shadow-lg animate-pulse`}>
              <Headphones className="w-8 h-8 text-white" />
            </div>
            <div className="text-center sm:text-left">
              <h1 className={`text-2xl sm:text-3xl font-bold bg-gradient-to-r ${theme.accent} bg-clip-text text-transparent`}>
                SoundWave Pro
              </h1>
              <p className={`text-sm ${theme.textSecondary}`}>Next-Gen Audio Converter</p>
            </div>
          </div>
          
          {/* Theme Controls */}
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setIsDark(!isDark)}
              className={`p-3 rounded-xl ${theme.cardBg} hover:scale-110 transition-all duration-300`}
            >
              {isDark ? <Sun className="w-5 h-5 text-yellow-400" /> : <Moon className="w-5 h-5 text-blue-400" />}
            </button>
            
            <select
              value={currentTheme}
              onChange={(e) => setCurrentTheme(e.target.value as ThemeType)}
              className={`${theme.cardBg} ${theme.text} px-4 py-2 rounded-xl border-0 focus:ring-2 focus:ring-opacity-50`}
            >
              {Object.entries(themes).map(([key, t]) => (
                <option key={key} value={key} className="bg-gray-800 text-white">{t.name}</option>
              ))}
            </select>
          </div>
        </div>
      </div>
    </header>
  );
};
