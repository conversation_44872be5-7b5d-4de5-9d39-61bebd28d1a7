import { ThemeConfig } from '@/types/soundcloud';

export const getThemes = (isDark: boolean): ThemeConfig => ({
  cyberpunk: {
    name: 'Cyberpunk',
    bg: isDark ? 'from-purple-900 via-blue-900 to-indigo-900' : 'from-purple-100 via-blue-100 to-indigo-100',
    cardBg: isDark ? 'bg-gray-900/80 backdrop-blur-xl border border-purple-500/20' : 'bg-white/80 backdrop-blur-xl border border-purple-200',
    accent: 'from-purple-500 to-cyan-500',
    text: isDark ? 'text-white' : 'text-gray-900',
    textSecondary: isDark ? 'text-gray-300' : 'text-gray-600'
  },
  neon: {
    name: 'Neon Dreams',
    bg: isDark ? 'from-black via-purple-900 to-pink-900' : 'from-pink-50 via-purple-50 to-indigo-50',
    cardBg: isDark ? 'bg-black/60 backdrop-blur-xl border border-pink-500/30' : 'bg-white/90 backdrop-blur-xl border border-pink-200',
    accent: 'from-pink-500 to-purple-500',
    text: isDark ? 'text-white' : 'text-gray-900',
    textSecondary: isDark ? 'text-pink-200' : 'text-gray-600'
  },
  ocean: {
    name: 'Ocean Breeze',
    bg: isDark ? 'from-slate-900 via-blue-900 to-teal-900' : 'from-blue-50 via-cyan-50 to-teal-50',
    cardBg: isDark ? 'bg-slate-800/70 backdrop-blur-xl border border-teal-500/20' : 'bg-white/90 backdrop-blur-xl border border-teal-200',
    accent: 'from-blue-500 to-teal-500',
    text: isDark ? 'text-white' : 'text-gray-900',
    textSecondary: isDark ? 'text-blue-200' : 'text-gray-600'
  },
  sunset: {
    name: 'Sunset Glow',
    bg: isDark ? 'from-orange-900 via-red-900 to-pink-900' : 'from-orange-50 via-red-50 to-pink-50',
    cardBg: isDark ? 'bg-orange-900/60 backdrop-blur-xl border border-orange-500/30' : 'bg-white/90 backdrop-blur-xl border border-orange-200',
    accent: 'from-orange-500 to-pink-500',
    text: isDark ? 'text-white' : 'text-gray-900',
    textSecondary: isDark ? 'text-orange-200' : 'text-gray-600'
  }
});
