import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { soundcloudDownloader } from '@/lib/soundcloud-downloader';
import { getDatabase } from '@/lib/database';
import { getFileStorage } from '@/lib/file-storage';

// Validation schema
const downloadSchema = z.object({
  url: z.string().url().refine(url => url.includes('soundcloud.com'), {
    message: 'Must be a valid SoundCloud URL'
  }),
  quality: z.enum(['128', '256', 'best']).default('128')
});

// Types
interface TrackInfo {
  id: string;
  title: string;
  artist: string;
  duration: number;
  artwork_url: string;
  stream_url?: string;
  download_url?: string;
  downloadable: boolean;
}

interface DownloadJob {
  id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  trackInfo?: TrackInfo;
  downloadUrl?: string;
  error?: string;
}

// Remove in-memory storage - now using SQLite database

// Download processor using database and file storage
class DownloadProcessor {
  async processDownload(jobId: string, url: string, quality: string): Promise<void> {
    const db = await getDatabase();
    const storage = getFileStorage();

    try {
      console.log('Starting real SoundCloud download for:', url);

      // Update job status to processing
      await db.updateJob(jobId, {
        status: 'processing',
        progress: 10
      });

      // Use real SoundCloud downloader
      const result = await soundcloudDownloader.downloadTrack(url);

      // Update job with track info
      await db.updateJob(jobId, {
        progress: 50,
        track_id: result.trackInfo.id.toString(),
        track_title: result.trackInfo.title,
        track_artist: result.trackInfo.user.username,
        track_duration: result.trackInfo.duration,
        track_artwork_url: result.trackInfo.artwork_url
      });

      // Save the downloaded file to disk
      const filename = `${result.trackInfo.user.username} - ${result.trackInfo.title}.mp3`;
      const fileResult = await storage.saveFile(result.audioData, filename);

      await db.updateJob(jobId, {
        progress: 80,
        file_path: fileResult.filePath,
        file_size: fileResult.fileSize
      });

      // Save file record to database
      await db.saveFile({
        id: fileResult.fileId,
        job_id: jobId,
        original_filename: filename,
        file_path: fileResult.filePath,
        file_size: fileResult.fileSize,
        mime_type: 'audio/mpeg'
      });

      // Complete the job
      await db.updateJob(jobId, {
        status: 'completed',
        progress: 100
      });

      console.log('Download completed successfully:', result.trackInfo.title);

    } catch (error) {
      console.error('Download processing error:', error);
      await db.updateJob(jobId, {
        status: 'failed',
        error_message: error instanceof Error ? error.message : 'Unknown error occurred'
      });
    }
  }
}

const downloadProcessor = new DownloadProcessor();

// API Routes
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { url, quality } = downloadSchema.parse(body);

    const db = await getDatabase();

    // Create download job
    const jobId = `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    await db.createJob({
      id: jobId,
      url,
      status: 'pending',
      progress: 0
    });

    // Start processing asynchronously (don't await)
    downloadProcessor.processDownload(jobId, url, quality).catch(error => {
      console.error('Download processing error:', error);
    });

    return NextResponse.json({
      success: true,
      jobId,
      message: 'Download job created successfully'
    });

  } catch (error) {
    console.error('API Error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: 'Invalid input',
        details: error.errors
      }, { status: 400 });
    }

    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const jobId = searchParams.get('jobId');

    if (!jobId) {
      return NextResponse.json({
        success: false,
        error: 'Job ID is required'
      }, { status: 400 });
    }

    const db = await getDatabase();
    const job = await db.getJob(jobId);

    if (!job) {
      return NextResponse.json({
        success: false,
        error: 'Job not found'
      }, { status: 404 });
    }

    // Convert database job to API format
    const apiJob = {
      id: job.id,
      status: job.status,
      progress: job.progress,
      trackInfo: job.track_title ? {
        id: job.track_id || '',
        title: job.track_title,
        artist: job.track_artist || '',
        duration: job.track_duration || 0,
        artwork_url: job.track_artwork_url || '',
        downloadable: true
      } : undefined,
      downloadUrl: job.status === 'completed' && job.file_path ? `/api/files/${job.id}.mp3` : undefined,
      error: job.error_message
    };

    return NextResponse.json({
      success: true,
      job: apiJob
    });
  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
