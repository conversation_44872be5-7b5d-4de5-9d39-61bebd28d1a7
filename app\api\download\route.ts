import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// Validation schema
const downloadSchema = z.object({
  url: z.string().url().refine(url => url.includes('soundcloud.com'), {
    message: 'Must be a valid SoundCloud URL'
  }),
  quality: z.enum(['128', '256', 'best']).default('128')
});

// Types
interface TrackInfo {
  id: string;
  title: string;
  artist: string;
  duration: number;
  artwork_url: string;
  stream_url?: string;
  download_url?: string;
  downloadable: boolean;
}

interface DownloadJob {
  id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  trackInfo?: TrackInfo;
  downloadUrl?: string;
  error?: string;
}

// In-memory storage (in production, use Redis or database)
const downloadJobs = new Map<string, DownloadJob>();

// Mock SoundCloud API client
class SoundCloudClient {
  private clientId = process.env.SOUNDCLOUD_CLIENT_ID || 'demo_client_id';

  async getTrackInfo(url: string): Promise<TrackInfo> {
    try {
      // Extract track ID from URL
      const trackId = this.extractTrackId(url);
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock response for demo
      const mockTrackInfo: TrackInfo = {
        id: trackId,
        title: "Demo Track - " + trackId.split('_')[1],
        artist: "Demo Artist - " + trackId.split('_')[0],
        duration: 180000, // 3 minutes in milliseconds
        artwork_url: "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=300&h=300&fit=crop",
        downloadable: true
      };

      return mockTrackInfo;
    } catch (error) {
      throw new Error('Failed to fetch track information');
    }
  }

  private extractTrackId(url: string): string {
    // Extract track ID from SoundCloud URL
    const match = url.match(/soundcloud\.com\/([^\/]+)\/([^\/\?]+)/);
    if (!match) {
      throw new Error('Invalid SoundCloud URL format');
    }
    return `${match[1]}_${match[2]}`;
  }

  async getStreamUrl(trackId: string): Promise<string> {
    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // In real implementation, this would extract the actual stream URL
    // For demo purposes, return a mock URL
    return `https://demo-stream.soundcloud.com/${trackId}.mp3`;
  }
}

// Download processor
class DownloadProcessor {
  private soundcloudClient = new SoundCloudClient();

  async processDownload(jobId: string, url: string, quality: string): Promise<void> {
    const job = downloadJobs.get(jobId);
    if (!job) return;

    try {
      // Update status to processing
      job.status = 'processing';
      job.progress = 10;

      // Get track info
      const trackInfo = await this.soundcloudClient.getTrackInfo(url);
      job.trackInfo = trackInfo;
      job.progress = 30;

      // Get stream URL
      const streamUrl = await this.soundcloudClient.getStreamUrl(trackInfo.id);
      job.progress = 50;

      // Download and convert (mock implementation)
      const downloadUrl = await this.downloadAndConvert(streamUrl, trackInfo, quality);
      job.progress = 90;

      // Complete the job
      job.status = 'completed';
      job.progress = 100;
      job.downloadUrl = downloadUrl;

    } catch (error) {
      job.status = 'failed';
      job.error = error instanceof Error ? error.message : 'Unknown error occurred';
    }
  }

  private async downloadAndConvert(streamUrl: string, trackInfo: TrackInfo, quality: string): Promise<string> {
    // Simulate download and conversion process
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // In real implementation:
    // 1. Download the stream using fetch or axios
    // 2. Convert using FFmpeg if needed
    // 3. Save to temporary storage
    // 4. Return download URL
    
    // For demo, return a mock download URL
    return `/api/files/${trackInfo.id}.mp3`;
  }
}

const downloadProcessor = new DownloadProcessor();

// API Routes
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { url, quality } = downloadSchema.parse(body);

    // Create download job
    const jobId = `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const job: DownloadJob = {
      id: jobId,
      status: 'pending',
      progress: 0
    };

    downloadJobs.set(jobId, job);

    // Start processing asynchronously (don't await)
    downloadProcessor.processDownload(jobId, url, quality).catch(error => {
      console.error('Download processing error:', error);
      const failedJob = downloadJobs.get(jobId);
      if (failedJob) {
        failedJob.status = 'failed';
        failedJob.error = 'Processing failed';
      }
    });

    return NextResponse.json({
      success: true,
      jobId,
      message: 'Download job created successfully'
    });

  } catch (error) {
    console.error('API Error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: 'Invalid input',
        details: error.errors
      }, { status: 400 });
    }

    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const jobId = searchParams.get('jobId');

    if (!jobId) {
      return NextResponse.json({
        success: false,
        error: 'Job ID is required'
      }, { status: 400 });
    }

    const job = downloadJobs.get(jobId);
    if (!job) {
      return NextResponse.json({
        success: false,
        error: 'Job not found'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      job
    });
  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}
