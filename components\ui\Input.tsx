'use client';

import React, { InputHTMLAttributes, ReactNode } from 'react';
import { cn } from '@/lib/utils';
import { useTheme } from '@/components/soundcloud/ThemeProvider';

interface InputProps extends InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  icon?: ReactNode;
  gradient?: string;
}

export const Input: React.FC<InputProps> = ({
  label,
  icon,
  gradient,
  className,
  ...props
}) => {
  const { theme } = useTheme();

  return (
    <div className="mb-8">
      {label && (
        <label className={`block text-sm font-semibold ${theme.text} mb-4 flex items-center`}>
          {icon && <span className="mr-2">{icon}</span>}
          {label}
        </label>
      )}
      <div className="relative">
        <input
          className={cn(
            `w-full px-6 py-6 bg-black/20 backdrop-blur-sm border-2 border-transparent rounded-2xl focus:border-purple-500/50 focus:outline-none focus:ring-2 focus:ring-purple-500/30 hover:bg-black/30 transition-all duration-300 text-lg ${theme.text} placeholder-gray-400`,
            className
          )}
          {...props}
        />
        {gradient && (
          <div className={`absolute inset-0 bg-gradient-to-r ${gradient} opacity-0 hover:opacity-10 rounded-2xl transition-opacity duration-300 pointer-events-none -z-10`}></div>
        )}
      </div>
    </div>
  );
};
