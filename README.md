# SoundWave Pro - SoundCloud Downloader UI

A modern, responsive SoundCloud downloader interface built with Next.js, React, and Tailwind CSS. This project showcases a clean, modular architecture with beautiful UI components and multiple theme options.

## 🚀 Features

- **Modern UI Design**: Beautiful, responsive interface with glassmorphism effects
- **Multiple Themes**: 4 different theme options (Cyberpunk, Neon Dreams, Ocean Breeze, Sunset Glow)
- **Dark/Light Mode**: Toggle between dark and light modes
- **Modular Architecture**: Clean, reusable component structure
- **TypeScript**: Full type safety throughout the application
- **Responsive Design**: Works perfectly on all device sizes
- **Animated Components**: Smooth animations and transitions

## 🏗️ Project Structure

```
├── app/
│   ├── page.tsx                    # Home page with navigation
│   ├── soundcloud/
│   │   └── page.tsx               # Main SoundCloud downloader page
│   ├── layout.tsx                 # Root layout
│   └── globals.css                # Global styles
├── components/
│   ├── ui/                        # Reusable UI components
│   │   ├── Button.tsx
│   │   ├── Input.tsx
│   │   └── Card.tsx
│   ├── soundcloud/                # SoundCloud-specific components
│   │   ├── ThemeProvider.tsx      # Theme context and provider
│   │   ├── FloatingParticles.tsx  # Animated background particles
│   │   ├── SearchForm.tsx         # URL input and search form
│   │   ├── SearchResults.tsx      # Search results display
│   │   ├── TrackCard.tsx          # Individual track component
│   │   ├── FeatureGrid.tsx        # Features showcase
│   │   └── HowItWorks.tsx         # How it works section
│   └── layout/                    # Layout components
│       ├── Header.tsx             # App header with theme controls
│       └── Footer.tsx             # App footer
├── types/
│   └── soundcloud.ts              # TypeScript type definitions
├── lib/
│   ├── themes.ts                  # Theme configurations
│   ├── constants.ts               # App constants and mock data
│   └── utils.ts                   # Utility functions
└── package.json
```

## 🛠️ Technologies Used

- **Next.js 15.3.3** - React framework with App Router
- **React 19** - UI library
- **TypeScript** - Type safety
- **Tailwind CSS 4** - Utility-first CSS framework
- **Lucide React** - Beautiful icons
- **Class Variance Authority** - Component variants
- **Tailwind Merge** - Conditional class merging

## 🎨 Components Overview

### Core Components

- **ThemeProvider**: Manages theme state and provides theme context
- **Button**: Reusable button with multiple variants and loading states
- **Input**: Styled input component with icon support
- **Card**: Flexible card component with hover effects

### SoundCloud Components

- **SearchForm**: Handles URL input and search functionality
- **TrackCard**: Displays individual track information with actions
- **FeatureGrid**: Showcases app features in a grid layout
- **HowItWorks**: Step-by-step guide component

## 🚀 Getting Started

1. **Install dependencies:**
   ```bash
   npm install
   # or
   pnpm install
   ```

2. **Run the development server:**
   ```bash
   npm run dev
   # or
   pnpm dev
   ```

3. **Open your browser:**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🎯 Usage

1. **Home Page**: Navigate between different tools (currently only SoundCloud downloader is available)
2. **SoundCloud Page**:
   - Paste a SoundCloud URL
   - Choose between single track or playlist mode
   - Click "Convert & Download" to process
   - View discovered tracks with download options
   - Switch between themes and dark/light modes

## 🎨 Themes

The application includes 4 beautiful themes:

1. **Cyberpunk**: Purple and cyan gradients with futuristic vibes
2. **Neon Dreams**: Pink and purple neon-inspired colors
3. **Ocean Breeze**: Blue and teal ocean-themed palette
4. **Sunset Glow**: Orange and pink sunset colors

Each theme automatically adapts to dark and light modes.

## 🔧 Customization

### Adding New Themes

1. Add theme configuration in `lib/themes.ts`
2. Update the `ThemeType` in `types/soundcloud.ts`
3. The theme will automatically appear in the theme selector

### Adding New Components

1. Create component in appropriate directory (`components/ui/`, `components/soundcloud/`, etc.)
2. Export from the component file
3. Import and use in your pages

## 📱 Responsive Design

The application is fully responsive and works on:
- Desktop (1024px+)
- Tablet (768px - 1023px)
- Mobile (320px - 767px)

## 🚀 Deployment

The app can be deployed on any platform that supports Next.js:

- **Vercel** (recommended)
- **Netlify**
- **Railway**
- **Docker**

## 📄 License

This project is for demonstration purposes and showcases modern React/Next.js development practices.

## 🤝 Contributing

Feel free to submit issues and enhancement requests!
