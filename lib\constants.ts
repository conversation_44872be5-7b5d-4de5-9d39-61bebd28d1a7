import { Track, Feature, HowItWorksStep } from '@/types/soundcloud';
import { Zap, Music, Volume2, Link, Download } from 'lucide-react';

export const mockTracks: Track[] = [
  {
    id: 1,
    title: "Midnight Synthwave",
    artist: "Neon Rider",
    duration: "4:23",
    plays: "2.3M",
    likes: "156K",
    artwork: "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=300&h=300&fit=crop"
  },
  {
    id: 2,
    title: "Digital Dreams",
    artist: "Cyber Phoenix",
    duration: "3:57",
    plays: "1.8M",
    likes: "98K",
    artwork: "https://images.unsplash.com/photo-1571330735066-03aaa9429d89?w=300&h=300&fit=crop"
  },
  {
    id: 3,
    title: "Neon Nights",
    artist: "Electric Soul",
    duration: "5:12",
    plays: "3.1M",
    likes: "234K",
    artwork: "https://images.unsplash.com/photo-1598488035139-bdbb2231ce04?w=300&h=300&fit=crop"
  }
];

export const features: Feature[] = [
  {
    icon: Zap,
    title: "Lightning Fast",
    description: "Download at blazing speeds with our optimized infrastructure",
    gradient: "from-yellow-400 to-orange-500"
  },
  {
    icon: Music,
    title: "Crystal Quality",
    description: "Preserve every note with lossless audio conversion technology",
    gradient: "from-green-400 to-blue-500"
  },
  {
    icon: Volume2,
    title: "Batch Processing",
    description: "Handle multiple tracks and playlists simultaneously",
    gradient: "from-purple-400 to-pink-500"
  }
];

export const howItWorksSteps: HowItWorksStep[] = [
  {
    step: "01",
    title: "Copy URL",
    description: "Grab the SoundCloud link from your browser",
    icon: Link
  },
  {
    step: "02", 
    title: "Paste & Convert",
    description: "Let our AI-powered engine work its magic",
    icon: Zap
  },
  {
    step: "03",
    title: "Download & Enjoy",
    description: "Get your high-quality MP3 instantly",
    icon: Download
  }
];
