import fs from 'fs/promises';
import path from 'path';
import crypto from 'crypto';

export class FileStorage {
  private storageDir: string;

  constructor() {
    this.storageDir = path.join(process.cwd(), 'data', 'downloads');
    this.ensureStorageDir();
  }

  private async ensureStorageDir(): Promise<void> {
    try {
      await fs.access(this.storageDir);
    } catch {
      await fs.mkdir(this.storageDir, { recursive: true });
      console.log('Created storage directory:', this.storageDir);
    }
  }

  async saveFile(data: ArrayBuffer, originalFilename: string): Promise<{
    fileId: string;
    filePath: string;
    fileSize: number;
  }> {
    await this.ensureStorageDir();

    // Generate unique file ID
    const fileId = crypto.randomUUID();
    const extension = path.extname(originalFilename) || '.mp3';
    const filename = `${fileId}${extension}`;
    const filePath = path.join(this.storageDir, filename);

    // Save file to disk
    const buffer = Buffer.from(data);
    await fs.writeFile(filePath, buffer);

    console.log(`File saved: ${filename} (${buffer.length} bytes)`);

    return {
      fileId,
      filePath,
      fileSize: buffer.length
    };
  }

  async getFile(fileId: string): Promise<{
    data: Buffer;
    exists: boolean;
  }> {
    try {
      // Find file with this ID (check different extensions)
      const files = await fs.readdir(this.storageDir);
      const matchingFile = files.find(file => file.startsWith(fileId));

      if (!matchingFile) {
        return { data: Buffer.alloc(0), exists: false };
      }

      const filePath = path.join(this.storageDir, matchingFile);
      const data = await fs.readFile(filePath);
      
      return { data, exists: true };
    } catch (error) {
      console.error('Error reading file:', error);
      return { data: Buffer.alloc(0), exists: false };
    }
  }

  async deleteFile(fileId: string): Promise<boolean> {
    try {
      const files = await fs.readdir(this.storageDir);
      const matchingFile = files.find(file => file.startsWith(fileId));

      if (!matchingFile) {
        return false;
      }

      const filePath = path.join(this.storageDir, matchingFile);
      await fs.unlink(filePath);
      
      console.log(`File deleted: ${matchingFile}`);
      return true;
    } catch (error) {
      console.error('Error deleting file:', error);
      return false;
    }
  }

  async cleanup(olderThanHours: number = 24): Promise<void> {
    try {
      const files = await fs.readdir(this.storageDir);
      const cutoffTime = Date.now() - (olderThanHours * 60 * 60 * 1000);

      for (const file of files) {
        const filePath = path.join(this.storageDir, file);
        const stats = await fs.stat(filePath);
        
        if (stats.mtime.getTime() < cutoffTime) {
          await fs.unlink(filePath);
          console.log(`Cleaned up old file: ${file}`);
        }
      }
    } catch (error) {
      console.error('Error during cleanup:', error);
    }
  }

  getStorageInfo(): { storageDir: string } {
    return { storageDir: this.storageDir };
  }
}

// Singleton instance
let storageInstance: FileStorage | null = null;

export function getFileStorage(): FileStorage {
  if (!storageInstance) {
    storageInstance = new FileStorage();
  }
  return storageInstance;
}
