'use client';

import React from 'react';
import { useTheme } from '@/components/soundcloud/ThemeProvider';

export const Footer: React.FC = () => {
  const { theme } = useTheme();

  return (
    <footer className={`${theme.cardBg} mt-20`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 text-center">
        <div className={`${theme.text} mb-6`}>
          <h3 className="text-2xl font-bold mb-2">SoundWave Pro</h3>
          <p className={`${theme.textSecondary}`}>
            This is a UI demonstration showcasing modern design principles
          </p>
        </div>
        <div className={`flex flex-wrap justify-center gap-8 text-sm ${theme.textSecondary}`}>
          <a href="#" className="hover:text-purple-400 transition-colors">Terms of Service</a>
          <a href="#" className="hover:text-purple-400 transition-colors">Privacy Policy</a>
          <a href="#" className="hover:text-purple-400 transition-colors">Support</a>
          <a href="#" className="hover:text-purple-400 transition-colors">API</a>
        </div>
      </div>
    </footer>
  );
};
