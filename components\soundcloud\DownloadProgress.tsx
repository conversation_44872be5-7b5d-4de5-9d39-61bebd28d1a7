'use client';

import React, { useEffect, useState } from 'react';
import { Download, CheckCircle, XCircle, Clock, Music, User } from 'lucide-react';
import { DownloadJob, downloadService } from '@/lib/download-service';
import { useTheme } from './ThemeProvider';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

interface DownloadProgressProps {
  jobId: string;
  onComplete?: (job: DownloadJob) => void;
  onError?: (error: string) => void;
}

export const DownloadProgress: React.FC<DownloadProgressProps> = ({
  jobId,
  onComplete,
  onError
}) => {
  const [job, setJob] = useState<DownloadJob | null>(null);
  const [isPolling, setIsPolling] = useState(true);
  const { theme } = useTheme();

  useEffect(() => {
    if (!jobId || !isPolling) return;

    const startPolling = async () => {
      try {
        await downloadService.pollJobStatus(
          jobId,
          (updatedJob) => {
            setJob(updatedJob);
          },
          1000 // Poll every second
        );
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        onError?.(errorMessage);
        setIsPolling(false);
      }
    };

    startPolling();

    return () => {
      setIsPolling(false);
    };
  }, [jobId, isPolling, onError]);

  useEffect(() => {
    if (job && (job.status === 'completed' || job.status === 'failed')) {
      setIsPolling(false);
      if (job.status === 'completed') {
        onComplete?.(job);
      } else if (job.status === 'failed') {
        onError?.(job.error || 'Download failed');
      }
    }
  }, [job, onComplete, onError]);

  const getStatusIcon = () => {
    if (!job) return <Clock className="w-6 h-6 animate-spin" />;
    
    switch (job.status) {
      case 'pending':
        return <Clock className="w-6 h-6 animate-pulse text-yellow-400" />;
      case 'processing':
        return <Download className="w-6 h-6 animate-bounce text-blue-400" />;
      case 'completed':
        return <CheckCircle className="w-6 h-6 text-green-400" />;
      case 'failed':
        return <XCircle className="w-6 h-6 text-red-400" />;
      default:
        return <Clock className="w-6 h-6" />;
    }
  };

  const getStatusText = () => {
    if (!job) return 'Initializing...';
    
    switch (job.status) {
      case 'pending':
        return 'Queued for processing...';
      case 'processing':
        return 'Processing your download...';
      case 'completed':
        return 'Download ready!';
      case 'failed':
        return 'Download failed';
      default:
        return 'Unknown status';
    }
  };

  const handleDownload = async () => {
    if (job?.downloadUrl && job?.trackInfo) {
      try {
        // Fetch the file and trigger download
        const response = await fetch(job.downloadUrl);
        const blob = await response.blob();

        // Create download link
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${job.trackInfo.artist} - ${job.trackInfo.title}.mp3`;

        // Trigger download
        document.body.appendChild(link);
        link.click();

        // Cleanup
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        console.log('Download completed successfully');
      } catch (error) {
        console.error('Download failed:', error);
        // Fallback to direct link
        window.open(job.downloadUrl, '_blank');
      }
    }
  };

  if (!job) {
    return (
      <Card padding="md" className="mb-6">
        <div className="flex items-center space-x-4">
          <Clock className="w-6 h-6 animate-spin text-blue-400" />
          <div>
            <p className={`${theme.text} font-medium`}>Initializing download...</p>
            <p className={`${theme.textSecondary} text-sm`}>Please wait while we process your request</p>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card padding="md" className="mb-6">
      <div className="space-y-4">
        {/* Header with status */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {getStatusIcon()}
            <div>
              <p className={`${theme.text} font-medium`}>{getStatusText()}</p>
              <p className={`${theme.textSecondary} text-sm`}>Job ID: {job.id}</p>
            </div>
          </div>
          
          {job.status === 'completed' && job.downloadUrl && (
            <Button
              onClick={handleDownload}
              gradient={theme.accent}
              size="sm"
              className="flex items-center"
            >
              <Download className="w-4 h-4 mr-2" />
              Download MP3
            </Button>
          )}
        </div>

        {/* Progress bar */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className={theme.textSecondary}>Progress</span>
            <span className={theme.textSecondary}>{job.progress}%</span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div
              className={`bg-gradient-to-r ${theme.accent} h-2 rounded-full transition-all duration-300 ease-out`}
              style={{ width: `${job.progress}%` }}
            />
          </div>
        </div>

        {/* Track info */}
        {job.trackInfo && (
          <div className="flex items-center space-x-4 p-4 bg-black/20 rounded-xl">
            <img
              src={job.trackInfo.artwork_url}
              alt={job.trackInfo.title}
              className="w-12 h-12 rounded-lg object-cover"
            />
            <div className="flex-1">
              <h4 className={`${theme.text} font-medium`}>{job.trackInfo.title}</h4>
              <div className="flex items-center space-x-4 text-sm">
                <span className={`${theme.textSecondary} flex items-center`}>
                  <User className="w-3 h-3 mr-1" />
                  {job.trackInfo.artist}
                </span>
                <span className={`${theme.textSecondary} flex items-center`}>
                  <Music className="w-3 h-3 mr-1" />
                  {downloadService.formatDuration(job.trackInfo.duration)}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Error message */}
        {job.status === 'failed' && job.error && (
          <div className="p-4 bg-red-500/20 border border-red-500/30 rounded-xl">
            <p className="text-red-400 text-sm">{job.error}</p>
          </div>
        )}
      </div>
    </Card>
  );
};
