'use client';

import React from 'react';
import { features } from '@/lib/constants';
import { useTheme } from './ThemeProvider';
import { Card } from '@/components/ui/Card';

export const FeatureGrid: React.FC = () => {
  const { theme } = useTheme();

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
      {features.map((feature, index) => (
        <Card key={index} hover padding="lg">
          <div className={`absolute inset-0 bg-gradient-to-r ${feature.gradient} opacity-0 group-hover:opacity-10 transition-opacity duration-300`}></div>
          <div className={`bg-gradient-to-r ${feature.gradient} w-16 h-16 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
            <feature.icon className="w-8 h-8 text-white" />
          </div>
          <h3 className={`text-xl font-bold ${theme.text} mb-4`}>{feature.title}</h3>
          <p className={`${theme.textSecondary} leading-relaxed`}>{feature.description}</p>
        </Card>
      ))}
    </div>
  );
};
