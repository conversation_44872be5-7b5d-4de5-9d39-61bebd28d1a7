import React, { useState, useEffect } from 'react';
import { Download, Music, Play, Pause, Volume2, Search, Link, Heart, Share2, Clock, User, Moon, Sun, Zap, Stars, Headphones } from 'lucide-react';

const SoundCloudDownloaderUI = () => {
  const [url, setUrl] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [searchResults, setSearchResults] = useState([]);
  const [activeTab, setActiveTab] = useState('single');
  const [isDark, setIsDark] = useState(true);
  const [currentTheme, setCurrentTheme] = useState('cyberpunk');

  const themes = {
    cyberpunk: {
      name: 'Cyberpunk',
      bg: isDark ? 'from-purple-900 via-blue-900 to-indigo-900' : 'from-purple-100 via-blue-100 to-indigo-100',
      cardBg: isDark ? 'bg-gray-900/80 backdrop-blur-xl border border-purple-500/20' : 'bg-white/80 backdrop-blur-xl border border-purple-200',
      accent: 'from-purple-500 to-cyan-500',
      text: isDark ? 'text-white' : 'text-gray-900',
      textSecondary: isDark ? 'text-gray-300' : 'text-gray-600'
    },
    neon: {
      name: 'Neon Dreams',
      bg: isDark ? 'from-black via-purple-900 to-pink-900' : 'from-pink-50 via-purple-50 to-indigo-50',
      cardBg: isDark ? 'bg-black/60 backdrop-blur-xl border border-pink-500/30' : 'bg-white/90 backdrop-blur-xl border border-pink-200',
      accent: 'from-pink-500 to-purple-500',
      text: isDark ? 'text-white' : 'text-gray-900',
      textSecondary: isDark ? 'text-pink-200' : 'text-gray-600'
    },
    ocean: {
      name: 'Ocean Breeze',
      bg: isDark ? 'from-slate-900 via-blue-900 to-teal-900' : 'from-blue-50 via-cyan-50 to-teal-50',
      cardBg: isDark ? 'bg-slate-800/70 backdrop-blur-xl border border-teal-500/20' : 'bg-white/90 backdrop-blur-xl border border-teal-200',
      accent: 'from-blue-500 to-teal-500',
      text: isDark ? 'text-white' : 'text-gray-900',
      textSecondary: isDark ? 'text-blue-200' : 'text-gray-600'
    },
    sunset: {
      name: 'Sunset Glow',
      bg: isDark ? 'from-orange-900 via-red-900 to-pink-900' : 'from-orange-50 via-red-50 to-pink-50',
      cardBg: isDark ? 'bg-orange-900/60 backdrop-blur-xl border border-orange-500/30' : 'bg-white/90 backdrop-blur-xl border border-orange-200',
      accent: 'from-orange-500 to-pink-500',
      text: isDark ? 'text-white' : 'text-gray-900',
      textSecondary: isDark ? 'text-orange-200' : 'text-gray-600'
    }
  };

  const theme = themes[currentTheme];

  const mockTracks = [
    {
      id: 1,
      title: "Midnight Synthwave",
      artist: "Neon Rider",
      duration: "4:23",
      plays: "2.3M",
      likes: "156K",
      artwork: "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=300&h=300&fit=crop"
    },
    {
      id: 2,
      title: "Digital Dreams",
      artist: "Cyber Phoenix",
      duration: "3:57",
      plays: "1.8M",
      likes: "98K",
      artwork: "https://images.unsplash.com/photo-1571330735066-03aaa9429d89?w=300&h=300&fit=crop"
    },
    {
      id: 3,
      title: "Neon Nights",
      artist: "Electric Soul",
      duration: "5:12",
      plays: "3.1M",
      likes: "234K",
      artwork: "https://images.unsplash.com/photo-1598488035139-bdbb2231ce04?w=300&h=300&fit=crop"
    }
  ];

  const handleSearch = () => {
    if (!url.trim()) return;
    setIsLoading(true);
    
    setTimeout(() => {
      setSearchResults(mockTracks);
      setIsLoading(false);
    }, 2000);
  };

  const handleDownload = (track) => {
    console.log('Download UI triggered for:', track.title);
  };

  // Floating particles animation
  const FloatingParticles = () => (
    <div className="fixed inset-0 overflow-hidden pointer-events-none">
      {[...Array(20)].map((_, i) => (
        <div
          key={i}
          className={`absolute w-2 h-2 bg-gradient-to-r ${theme.accent} rounded-full opacity-20 animate-pulse`}
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
            animationDelay: `${Math.random() * 5}s`,
            animationDuration: `${3 + Math.random() * 4}s`
          }}
        />
      ))}
    </div>
  );

  return (
    <div className={`min-h-screen bg-gradient-to-br ${theme.bg} transition-all duration-700 relative overflow-hidden`}>
      <FloatingParticles />
      
      {/* Animated Background Elements */}
      <div className="fixed inset-0 opacity-10">
        <div className={`absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r ${theme.accent} rounded-full blur-3xl animate-pulse`}></div>
        <div className={`absolute bottom-1/4 right-1/4 w-64 h-64 bg-gradient-to-r ${theme.accent} rounded-full blur-3xl animate-pulse`} style={{animationDelay: '2s'}}></div>
      </div>

      {/* Header */}
      <header className={`${theme.cardBg} sticky top-0 z-50 transition-all duration-500`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
            <div className="flex items-center space-x-4">
              <div className={`bg-gradient-to-r ${theme.accent} p-3 rounded-2xl shadow-lg animate-pulse`}>
                <Headphones className="w-8 h-8 text-white" />
              </div>
              <div className="text-center sm:text-left">
                <h1 className={`text-2xl sm:text-3xl font-bold bg-gradient-to-r ${theme.accent} bg-clip-text text-transparent`}>
                  SoundWave Pro
                </h1>
                <p className={`text-sm ${theme.textSecondary}`}>Next-Gen Audio Converter</p>
              </div>
            </div>
            
            {/* Theme Controls */}
            <div className="flex items-center space-x-3">
              <button
                onClick={() => setIsDark(!isDark)}
                className={`p-3 rounded-xl ${theme.cardBg} hover:scale-110 transition-all duration-300`}
              >
                {isDark ? <Sun className="w-5 h-5 text-yellow-400" /> : <Moon className="w-5 h-5 text-blue-400" />}
              </button>
              
              <select
                value={currentTheme}
                onChange={(e) => setCurrentTheme(e.target.value)}
                className={`${theme.cardBg} ${theme.text} px-4 py-2 rounded-xl border-0 focus:ring-2 focus:ring-opacity-50`}
              >
                {Object.entries(themes).map(([key, t]) => (
                  <option key={key} value={key} className="bg-gray-800 text-white">{t.name}</option>
                ))}
              </select>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <h2 className={`text-4xl sm:text-6xl font-bold ${theme.text} mb-4 animate-fade-in`}>
            Transform Your
            <span className={`bg-gradient-to-r ${theme.accent} bg-clip-text text-transparent ml-3`}>
              Sound Experience
            </span>
          </h2>
          <p className={`text-lg sm:text-xl ${theme.textSecondary} max-w-2xl mx-auto`}>
            Convert and download your favorite tracks with cutting-edge technology
          </p>
        </div>

        {/* Main Download Section */}
        <div className={`${theme.cardBg} rounded-3xl shadow-2xl p-6 sm:p-8 lg:p-12 mb-12 relative overflow-hidden`}>
          {/* Animated border */}
          <div className={`absolute inset-0 bg-gradient-to-r ${theme.accent} opacity-20 animate-pulse rounded-3xl`}></div>
          <div className="relative z-10">
            {/* Tabs */}
            <div className="flex flex-col sm:flex-row mb-8 bg-black/20 p-2 rounded-2xl backdrop-blur-sm">
              {[
                { key: 'single', label: 'Single Track', icon: Music },
                { key: 'playlist', label: 'Playlist', icon: Volume2 }
              ].map(({ key, label, icon: Icon }) => (
                <button
                  key={key}
                  onClick={() => setActiveTab(key)}
                  className={`flex-1 flex items-center justify-center py-4 px-6 rounded-xl transition-all duration-300 ${
                    activeTab === key
                      ? `bg-gradient-to-r ${theme.accent} shadow-lg text-white transform scale-105`
                      : `${theme.textSecondary} hover:bg-white/10`
                  }`}
                >
                  <Icon className="w-5 h-5 mr-2" />
                  <span className="font-medium">{label}</span>
                </button>
              ))}
            </div>

            {/* URL Input */}
            <div className="mb-8">
              <label className={`block text-sm font-semibold ${theme.text} mb-4 flex items-center`}>
                <Link className="w-5 h-5 mr-2" />
                Paste SoundCloud URL
              </label>
              <div className="relative group">
                <input
                  type="text"
                  value={url}
                  onChange={(e) => setUrl(e.target.value)}
                  placeholder="https://soundcloud.com/artist/track-name"
                  className={`w-full px-6 py-6 bg-black/20 backdrop-blur-sm border-2 border-transparent rounded-2xl focus:border-opacity-50 transition-all duration-300 text-lg ${theme.text} placeholder-gray-400 group-hover:bg-black/30`}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                />
                <div className={`absolute inset-0 bg-gradient-to-r ${theme.accent} opacity-0 group-hover:opacity-20 rounded-2xl transition-opacity duration-300`}></div>
              </div>
            </div>

            {/* Download Button */}
            <button
              onClick={handleSearch}
              disabled={!url.trim() || isLoading}
              className={`w-full bg-gradient-to-r ${theme.accent} hover:shadow-2xl disabled:opacity-50 text-white text-xl font-bold py-6 px-8 rounded-2xl transition-all duration-300 transform hover:scale-105 disabled:scale-100 shadow-lg flex items-center justify-center relative overflow-hidden group`}
            >
              <div className="absolute inset-0 bg-white/20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mr-4"></div>
                  <span className="relative z-10">Processing Magic...</span>
                </>
              ) : (
                <>
                  <Zap className="w-8 h-8 mr-4 animate-pulse" />
                  <span className="relative z-10">Convert & Download</span>
                </>
              )}
            </button>
          </div>
        </div>

        {/* Search Results */}
        {searchResults.length > 0 && (
          <div className={`${theme.cardBg} rounded-3xl shadow-2xl p-6 sm:p-8 mb-12`}>
            <h2 className={`text-2xl sm:text-3xl font-bold ${theme.text} mb-8 flex items-center`}>
              <Stars className="w-8 h-8 mr-3 text-yellow-400" />
              Discovered Tracks
            </h2>
            
            <div className="grid gap-6">
              {searchResults.map((track, index) => (
                <div 
                  key={track.id} 
                  className={`flex flex-col sm:flex-row items-center p-6 bg-black/20 backdrop-blur-sm rounded-2xl hover:bg-black/30 transition-all duration-300 transform hover:scale-105`}
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  {/* Artwork */}
                  <div className="relative mb-4 sm:mb-0 sm:mr-6 group">
                    <img 
                      src={track.artwork} 
                      alt={track.title}
                      className="w-20 h-20 sm:w-24 sm:h-24 rounded-2xl object-cover shadow-lg"
                    />
                    <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-0 group-hover:bg-opacity-60 rounded-2xl transition-all cursor-pointer">
                      <Play className="w-8 h-8 text-white opacity-0 group-hover:opacity-100 transition-opacity transform group-hover:scale-110" />
                    </div>
                    <div className={`absolute -top-2 -right-2 bg-gradient-to-r ${theme.accent} w-6 h-6 rounded-full animate-pulse`}></div>
                  </div>

                  {/* Track Info */}
                  <div className="flex-1 text-center sm:text-left mb-4 sm:mb-0">
                    <h3 className={`font-bold ${theme.text} text-lg sm:text-xl mb-2`}>{track.title}</h3>
                    <div className={`flex flex-wrap items-center justify-center sm:justify-start ${theme.textSecondary} text-sm gap-4`}>
                      <span className="flex items-center">
                        <User className="w-4 h-4 mr-1" />
                        {track.artist}
                      </span>
                      <span className="flex items-center">
                        <Clock className="w-4 h-4 mr-1" />
                        {track.duration}
                      </span>
                      <span className="flex items-center">
                        <Play className="w-4 h-4 mr-1" />
                        {track.plays}
                      </span>
                      <span className="flex items-center">
                        <Heart className="w-4 h-4 mr-1" />
                        {track.likes}
                      </span>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center space-x-3">
                    <button className={`p-3 ${theme.textSecondary} hover:text-red-400 transition-colors transform hover:scale-110`}>
                      <Heart className="w-6 h-6" />
                    </button>
                    <button className={`p-3 ${theme.textSecondary} hover:text-blue-400 transition-colors transform hover:scale-110`}>
                      <Share2 className="w-6 h-6" />
                    </button>
                    <button
                      onClick={() => handleDownload(track)}
                      className={`bg-gradient-to-r ${theme.accent} hover:shadow-xl text-white px-6 py-3 rounded-xl flex items-center transition-all transform hover:scale-110 font-semibold`}
                    >
                      <Download className="w-5 h-5 mr-2" />
                      Download
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {[
            {
              icon: Zap,
              title: "Lightning Fast",
              description: "Download at blazing speeds with our optimized infrastructure",
              gradient: "from-yellow-400 to-orange-500"
            },
            {
              icon: Music,
              title: "Crystal Quality",
              description: "Preserve every note with lossless audio conversion technology",
              gradient: "from-green-400 to-blue-500"
            },
            {
              icon: Volume2,
              title: "Batch Processing",
              description: "Handle multiple tracks and playlists simultaneously",
              gradient: "from-purple-400 to-pink-500"
            }
          ].map((feature, index) => (
            <div 
              key={index} 
              className={`${theme.cardBg} p-8 rounded-2xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 group relative overflow-hidden`}
            >
              <div className={`absolute inset-0 bg-gradient-to-r ${feature.gradient} opacity-0 group-hover:opacity-10 transition-opacity duration-300`}></div>
              <div className={`bg-gradient-to-r ${feature.gradient} w-16 h-16 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                <feature.icon className="w-8 h-8 text-white" />
              </div>
              <h3 className={`text-xl font-bold ${theme.text} mb-4`}>{feature.title}</h3>
              <p className={`${theme.textSecondary} leading-relaxed`}>{feature.description}</p>
            </div>
          ))}
        </div>

        {/* How It Works */}
        <div className={`${theme.cardBg} rounded-3xl shadow-2xl p-8 sm:p-12`}>
          <h2 className={`text-3xl sm:text-4xl font-bold ${theme.text} mb-12 text-center`}>
            How It Works
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 sm:gap-12">
            {[
              {
                step: "01",
                title: "Copy URL",
                description: "Grab the SoundCloud link from your browser",
                icon: Link
              },
              {
                step: "02", 
                title: "Paste & Convert",
                description: "Let our AI-powered engine work its magic",
                icon: Zap
              },
              {
                step: "03",
                title: "Download & Enjoy",
                description: "Get your high-quality MP3 instantly",
                icon: Download
              }
            ].map((item, index) => (
              <div key={index} className="text-center group">
                <div className={`relative mx-auto mb-6 w-24 h-24 bg-gradient-to-r ${theme.accent} rounded-3xl flex items-center justify-center shadow-lg group-hover:shadow-2xl transition-all duration-300 transform group-hover:scale-110`}>
                  <item.icon className="w-10 h-10 text-white" />
                  <div className={`absolute -top-2 -right-2 bg-gradient-to-r from-white to-gray-200 text-gray-800 w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold`}>
                    {item.step}
                  </div>
                </div>
                <h3 className={`text-xl font-bold ${theme.text} mb-4`}>{item.title}</h3>
                <p className={`${theme.textSecondary} leading-relaxed`}>{item.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className={`${theme.cardBg} mt-20`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 text-center">
          <div className={`${theme.text} mb-6`}>
            <h3 className="text-2xl font-bold mb-2">SoundWave Pro</h3>
            <p className={`${theme.textSecondary}`}>
              This is a UI demonstration showcasing modern design principles
            </p>
          </div>
          <div className={`flex flex-wrap justify-center gap-8 text-sm ${theme.textSecondary}`}>
            <a href="#" className="hover:text-purple-400 transition-colors">Terms of Service</a>
            <a href="#" className="hover:text-purple-400 transition-colors">Privacy Policy</a>
            <a href="#" className="hover:text-purple-400 transition-colors">Support</a>
            <a href="#" className="hover:text-purple-400 transition-colors">API</a>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default SoundCloudDownloaderUI;