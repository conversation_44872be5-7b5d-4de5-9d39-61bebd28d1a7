'use client';

import React, { ReactNode } from 'react';
import { cn } from '@/lib/utils';
import { useTheme } from '@/components/soundcloud/ThemeProvider';

interface CardProps {
  children: ReactNode;
  className?: string;
  hover?: boolean;
  gradient?: boolean;
  padding?: 'sm' | 'md' | 'lg' | 'xl';
}

export const Card: React.FC<CardProps> = ({
  children,
  className,
  hover = false,
  gradient = false,
  padding = 'lg'
}) => {
  const { theme } = useTheme();

  const paddingClasses = {
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-6 sm:p-8 lg:p-12',
    xl: 'p-8 sm:p-12'
  };

  return (
    <div 
      className={cn(
        `${theme.cardBg} rounded-3xl shadow-2xl relative overflow-hidden`,
        paddingClasses[padding],
        hover && 'hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 group',
        className
      )}
    >
      {gradient && (
        <div className={`absolute inset-0 bg-gradient-to-r ${theme.accent} opacity-20 animate-pulse rounded-3xl pointer-events-none`}></div>
      )}
      <div className="relative z-10">
        {children}
      </div>
    </div>
  );
};
