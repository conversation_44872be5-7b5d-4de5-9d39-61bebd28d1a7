'use client';

import React, { useState } from 'react';
import { Music, Volume2, Link, Zap, Download, Settings } from 'lucide-react';
import { TabType } from '@/types/soundcloud';
import { useTheme } from './ThemeProvider';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { downloadService, DownloadRequest } from '@/lib/download-service';
import { useToast, toast } from '@/components/ui/Toast';

interface SearchFormProps {
  onSearch: (url: string) => void;
  onDownloadStart?: (jobId: string, url: string) => void;
  isLoading: boolean;
}

export const SearchForm: React.FC<SearchFormProps> = ({ onSearch, onDownloadStart, isLoading }) => {
  const [url, setUrl] = useState('');
  const [activeTab, setActiveTab] = useState<TabType>('single');
  const [quality, setQuality] = useState<'128' | '256' | 'best'>('128');
  const [isDownloading, setIsDownloading] = useState(false);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const { theme } = useTheme();
  const { addToast } = useToast();

  const handleSearch = () => {
    if (!url.trim()) return;
    onSearch(url);
  };

  const handleDownload = async () => {
    if (!url.trim()) return;

    // Validate URL
    if (!downloadService.validateSoundCloudUrl(url)) {
      addToast(toast.error('Invalid URL', 'Please enter a valid SoundCloud URL'));
      return;
    }

    setIsDownloading(true);

    try {
      const request: DownloadRequest = {
        url: url.trim(),
        quality
      };

      const response = await downloadService.startDownload(request);

      if (response.success && response.jobId) {
        onDownloadStart?.(response.jobId, url);
        setUrl(''); // Clear the input after successful start
        addToast(toast.success('Download Started', 'Your track is being processed'));
      } else {
        addToast(toast.error('Download Failed', response.error || 'Failed to start download'));
      }
    } catch (error) {
      addToast(toast.error('Network Error', 'An error occurred while starting the download'));
    } finally {
      setIsDownloading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      if (e.shiftKey) {
        handleDownload();
      } else {
        handleSearch();
      }
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setUrl(e.target.value);
  };

  return (
    <Card gradient className="mb-12">
      {/* Tabs */}
      <div className="flex flex-col sm:flex-row mb-8 bg-black/20 p-2 rounded-2xl backdrop-blur-sm">
        {[
          { key: 'single' as TabType, label: 'Single Track', icon: Music },
          { key: 'playlist' as TabType, label: 'Playlist', icon: Volume2 }
        ].map(({ key, label, icon: Icon }) => (
          <button
            key={key}
            onClick={() => setActiveTab(key)}
            className={`flex-1 flex items-center justify-center py-4 px-6 rounded-xl transition-all duration-300 ${
              activeTab === key
                ? `bg-gradient-to-r ${theme.accent} shadow-lg text-white transform scale-105`
                : `${theme.textSecondary} hover:bg-white/10`
            }`}
          >
            <Icon className="w-5 h-5 mr-2" />
            <span className="font-medium">{label}</span>
          </button>
        ))}
      </div>

      {/* URL Input */}
      <div className="mb-8">
        <label className={`block text-sm font-semibold ${theme.text} mb-4 flex items-center`}>
          <Link className="w-5 h-5 mr-2" />
          Paste SoundCloud URL
        </label>
        <input
          type="text"
          value={url}
          onChange={handleInputChange}
          onKeyPress={handleKeyPress}
          placeholder="https://soundcloud.com/artist/track-name"
          autoComplete="off"
          className={`w-full px-6 py-6 bg-black/20 backdrop-blur-sm border-2 border-transparent rounded-2xl focus:border-purple-500/50 focus:outline-none focus:ring-2 focus:ring-purple-500/30 hover:bg-black/30 transition-all duration-300 text-lg ${theme.text} placeholder-gray-400`}
        />
      </div>

      {/* Advanced Settings */}
      {showAdvanced && (
        <div className="mb-6 p-4 bg-black/20 rounded-xl">
          <h4 className={`${theme.text} font-medium mb-3`}>Download Settings</h4>
          <div className="space-y-3">
            <div>
              <label className={`block text-sm ${theme.textSecondary} mb-2`}>
                Audio Quality
              </label>
              <select
                value={quality}
                onChange={(e) => setQuality(e.target.value as '128' | '256' | 'best')}
                className={`w-full px-3 py-2 bg-black/30 ${theme.text} rounded-lg border border-gray-600 focus:border-purple-500 focus:outline-none`}
              >
                <option value="128">128 kbps (Standard)</option>
                <option value="256">256 kbps (High Quality)</option>
                <option value="best">Best Available</option>
              </select>
            </div>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="space-y-3">
        <div className="flex space-x-3">
          <Button
            onClick={handleSearch}
            disabled={!url.trim() || isLoading}
            isLoading={isLoading}
            variant="secondary"
            size="lg"
            className="flex-1"
          >
            {isLoading ? (
              <>Searching...</>
            ) : (
              <>
                <Zap className="w-6 h-6 mr-2" />
                Preview Track
              </>
            )}
          </Button>

          <Button
            onClick={handleDownload}
            disabled={!url.trim() || isDownloading}
            isLoading={isDownloading}
            gradient={theme.accent}
            size="lg"
            className="flex-1"
          >
            {isDownloading ? (
              <>Starting Download...</>
            ) : (
              <>
                <Download className="w-6 h-6 mr-2" />
                Download MP3
              </>
            )}
          </Button>
        </div>

        <div className="flex justify-between items-center">
          <button
            onClick={() => setShowAdvanced(!showAdvanced)}
            className={`flex items-center text-sm ${theme.textSecondary} hover:${theme.text} transition-colors`}
          >
            <Settings className="w-4 h-4 mr-1" />
            {showAdvanced ? 'Hide' : 'Show'} Advanced Settings
          </button>

          <p className={`text-xs ${theme.textSecondary}`}>
            Press Enter to preview, Shift+Enter to download
          </p>
        </div>
      </div>
    </Card>
  );
};
