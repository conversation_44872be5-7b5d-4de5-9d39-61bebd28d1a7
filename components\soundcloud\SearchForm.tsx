'use client';

import React, { useState } from 'react';
import { Music, Volume2, Link, Zap } from 'lucide-react';
import { TabType } from '@/types/soundcloud';
import { useTheme } from './ThemeProvider';
import { Input } from '@/components/ui/Input';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';

interface SearchFormProps {
  onSearch: (url: string) => void;
  isLoading: boolean;
}

export const SearchForm: React.FC<SearchFormProps> = ({ onSearch, isLoading }) => {
  const [url, setUrl] = useState('');
  const [activeTab, setActiveTab] = useState<TabType>('single');
  const { theme } = useTheme();

  const handleSubmit = () => {
    if (!url.trim()) return;
    onSearch(url);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSubmit();
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setUrl(e.target.value);
  };

  return (
    <Card gradient className="mb-12">
      {/* Tabs */}
      <div className="flex flex-col sm:flex-row mb-8 bg-black/20 p-2 rounded-2xl backdrop-blur-sm">
        {[
          { key: 'single' as TabType, label: 'Single Track', icon: Music },
          { key: 'playlist' as TabType, label: 'Playlist', icon: Volume2 }
        ].map(({ key, label, icon: Icon }) => (
          <button
            key={key}
            onClick={() => setActiveTab(key)}
            className={`flex-1 flex items-center justify-center py-4 px-6 rounded-xl transition-all duration-300 ${
              activeTab === key
                ? `bg-gradient-to-r ${theme.accent} shadow-lg text-white transform scale-105`
                : `${theme.textSecondary} hover:bg-white/10`
            }`}
          >
            <Icon className="w-5 h-5 mr-2" />
            <span className="font-medium">{label}</span>
          </button>
        ))}
      </div>

      {/* URL Input */}
      <div className="mb-8">
        <label className={`block text-sm font-semibold ${theme.text} mb-4 flex items-center`}>
          <Link className="w-5 h-5 mr-2" />
          Paste SoundCloud URL
        </label>
        <input
          type="text"
          value={url}
          onChange={handleInputChange}
          onKeyPress={handleKeyPress}
          placeholder="https://soundcloud.com/artist/track-name"
          autoComplete="off"
          className={`w-full px-6 py-6 bg-black/20 backdrop-blur-sm border-2 border-transparent rounded-2xl focus:border-purple-500/50 focus:outline-none focus:ring-2 focus:ring-purple-500/30 hover:bg-black/30 transition-all duration-300 text-lg ${theme.text} placeholder-gray-400`}
        />
      </div>

      {/* Download Button */}
      <Button
        onClick={handleSubmit}
        disabled={!url.trim() || isLoading}
        isLoading={isLoading}
        gradient={theme.accent}
        size="lg"
        className="w-full"
      >
        {isLoading ? (
          <>Processing Magic...</>
        ) : (
          <>
            <Zap className="w-8 h-8 mr-4 animate-pulse" />
            Convert & Download
          </>
        )}
      </Button>
    </Card>
  );
};
