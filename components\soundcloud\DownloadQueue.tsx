'use client';

import React, { useState, useEffect } from 'react';
import { Trash2, Download, List, X } from 'lucide-react';
import { DownloadJob } from '@/lib/download-service';
import { useTheme } from './ThemeProvider';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { useToast } from '@/components/ui/Toast';
import { DownloadProgress } from './DownloadProgress';

interface QueueItem {
  id: string;
  jobId: string;
  url: string;
  addedAt: Date;
  job?: DownloadJob;
}

interface DownloadQueueProps {
  isVisible: boolean;
  onToggle: () => void;
  queueItems: QueueItem[];
  onAddToQueue: (jobId: string, url: string) => void;
  onRemoveFromQueue: (id: string) => void;
  onClearCompleted: () => void;
  onClearAll: () => void;
}

export const DownloadQueue: React.FC<DownloadQueueProps> = ({
  isVisible,
  onToggle,
  queueItems,
  onAddToQueue,
  onRemoveFromQueue,
  onClearCompleted,
  onClearAll
}) => {
  const { theme } = useTheme();
  const { showToast } = useToast();

  // Handle ESC key to close modal
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isVisible) {
        onToggle();
      }
    };

    if (isVisible) {
      document.addEventListener('keydown', handleKeyDown);
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [isVisible, onToggle]);

  const handleJobComplete = (itemId: string, job: DownloadJob) => {
    // This will be handled by parent component
    console.log('Job completed:', itemId, job);
  };

  const handleJobError = (itemId: string, error: string) => {
    // This will be handled by parent component
    console.log('Job error:', itemId, error);
  };

  const getQueueStats = () => {
    const total = queueItems.length;
    const completed = queueItems.filter(item => item.job?.status === 'completed').length;
    const failed = queueItems.filter(item => item.job?.status === 'failed').length;
    const processing = queueItems.filter(item =>
      item.job?.status === 'processing' || item.job?.status === 'pending'
    ).length;

    return { total, completed, failed, processing };
  };

  const stats = getQueueStats();

  if (!isVisible) {
    return (
      <div className="fixed bottom-6 right-6 z-50">
        <Button
          onClick={onToggle}
          gradient={theme.accent}
          size="md"
          className="rounded-full shadow-lg"
        >
          <List className="w-5 h-5 mr-2" />
          Queue ({queueItems.length})
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-4xl max-h-[80vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className={`text-2xl font-bold ${theme.text} flex items-center`}>
              <Download className="w-6 h-6 mr-3" />
              Download Queue
            </h2>
            <div className="flex items-center space-x-4 mt-2 text-sm">
              <span className={theme.textSecondary}>
                Total: {stats.total}
              </span>
              <span className="text-blue-400">
                Processing: {stats.processing}
              </span>
              <span className="text-green-400">
                Completed: {stats.completed}
              </span>
              <span className="text-red-400">
                Failed: {stats.failed}
              </span>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {stats.completed > 0 && (
              <Button
                onClick={onClearCompleted}
                variant="secondary"
                size="sm"
                className='text-white'
              >
                Clear Completed
              </Button>
            )}
            {queueItems.length > 0 && (
              <Button
                onClick={onClearAll}
                variant="secondary"
                size="sm"
                className='text-white'
              >
                Clear All
              </Button>
            )}
            <Button
              onClick={onToggle}
              variant="ghost"
              size="sm"
              className="p-2 text-white"
            >
              <X className="w-5 h-5" />
            </Button>
          </div>
        </div>

        {/* Queue Items */}
        <div className="space-y-4 max-h-[60vh] overflow-y-auto">
          {queueItems.length === 0 ? (
            <div className="text-center py-12">
              <Download className={`w-16 h-16 ${theme.textSecondary} mx-auto mb-4 opacity-50`} />
              <p className={`${theme.textSecondary} text-lg`}>No downloads in queue</p>
              <p className={`${theme.textSecondary} text-sm mt-2`}>
                Start downloading tracks to see them here
              </p>
            </div>
          ) : (
            queueItems.map((item) => (
              <div key={item.id} className="relative">
                <div className="flex items-start justify-between mb-2">
                  <div className="flex-1 min-w-0">
                    <p
                      className={`${theme.text} text-sm font-medium truncate cursor-pointer hover:text-blue-400 transition-colors`}
                      title={item.url}
                      onClick={async () => {
                        try {
                          await navigator.clipboard.writeText(item.url);
                          showToast('URL copied to clipboard!', 'success');
                        } catch (error) {
                          showToast('Failed to copy URL', 'error');
                        }
                      }}
                    >
                      {item.url.length > 60 ? `${item.url.substring(0, 60)}...` : item.url}
                    </p>
                    <p className={`${theme.textSecondary} text-xs`}>
                      Added: {item.addedAt.toLocaleTimeString()} • Click URL to copy
                    </p>
                  </div>
                  <Button
                    onClick={() => onRemoveFromQueue(item.id)}
                    variant="ghost"
                    size="sm"
                    className="p-1 ml-2"
                  >
                    <Trash2 className="w-4 h-4 text-red-400" />
                  </Button>
                </div>

                <DownloadProgress
                  jobId={item.jobId}
                  url={item.url}
                  onComplete={(job) => handleJobComplete(item.id, job)}
                  onError={(error) => handleJobError(item.id, error)}
                />
              </div>
            ))
          )}
        </div>
      </Card>
    </div>
  );
};
