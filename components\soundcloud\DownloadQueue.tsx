'use client';

import React, { useState } from 'react';
import { Trash2, Download, List, X } from 'lucide-react';
import { DownloadJob } from '@/lib/download-service';
import { useTheme } from './ThemeProvider';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { DownloadProgress } from './DownloadProgress';

interface QueueItem {
  id: string;
  jobId: string;
  url: string;
  addedAt: Date;
  job?: DownloadJob;
}

interface DownloadQueueProps {
  isVisible: boolean;
  onToggle: () => void;
}

export const DownloadQueue: React.FC<DownloadQueueProps> = ({
  isVisible,
  onToggle
}) => {
  const [queueItems, setQueueItems] = useState<QueueItem[]>([]);
  const { theme } = useTheme();

  const addToQueue = (jobId: string, url: string) => {
    const newItem: QueueItem = {
      id: `queue_${Date.now()}`,
      jobId,
      url,
      addedAt: new Date()
    };
    setQueueItems(prev => [...prev, newItem]);
  };

  const removeFromQueue = (id: string) => {
    setQueueItems(prev => prev.filter(item => item.id !== id));
  };

  const clearCompleted = () => {
    setQueueItems(prev => 
      prev.filter(item => 
        !item.job || (item.job.status !== 'completed' && item.job.status !== 'failed')
      )
    );
  };

  const clearAll = () => {
    setQueueItems([]);
  };

  const handleJobComplete = (itemId: string, job: DownloadJob) => {
    setQueueItems(prev =>
      prev.map(item =>
        item.id === itemId ? { ...item, job } : item
      )
    );
  };

  const handleJobError = (itemId: string, error: string) => {
    setQueueItems(prev =>
      prev.map(item =>
        item.id === itemId 
          ? { 
              ...item, 
              job: { 
                id: item.jobId, 
                status: 'failed', 
                progress: 0, 
                error 
              } as DownloadJob 
            } 
          : item
      )
    );
  };

  const getQueueStats = () => {
    const total = queueItems.length;
    const completed = queueItems.filter(item => item.job?.status === 'completed').length;
    const failed = queueItems.filter(item => item.job?.status === 'failed').length;
    const processing = queueItems.filter(item => 
      item.job?.status === 'processing' || item.job?.status === 'pending'
    ).length;

    return { total, completed, failed, processing };
  };

  const stats = getQueueStats();

  if (!isVisible) {
    return (
      <div className="fixed bottom-6 right-6 z-50">
        <Button
          onClick={onToggle}
          gradient={theme.accent}
          size="md"
          className="rounded-full shadow-lg"
        >
          <List className="w-5 h-5 mr-2" />
          Queue ({queueItems.length})
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-4xl max-h-[80vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className={`text-2xl font-bold ${theme.text} flex items-center`}>
              <Download className="w-6 h-6 mr-3" />
              Download Queue
            </h2>
            <div className="flex items-center space-x-4 mt-2 text-sm">
              <span className={theme.textSecondary}>
                Total: {stats.total}
              </span>
              <span className="text-blue-400">
                Processing: {stats.processing}
              </span>
              <span className="text-green-400">
                Completed: {stats.completed}
              </span>
              <span className="text-red-400">
                Failed: {stats.failed}
              </span>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {stats.completed > 0 && (
              <Button
                onClick={clearCompleted}
                variant="secondary"
                size="sm"
              >
                Clear Completed
              </Button>
            )}
            {queueItems.length > 0 && (
              <Button
                onClick={clearAll}
                variant="secondary"
                size="sm"
              >
                Clear All
              </Button>
            )}
            <Button
              onClick={onToggle}
              variant="ghost"
              size="sm"
              className="p-2"
            >
              <X className="w-5 h-5" />
            </Button>
          </div>
        </div>

        {/* Queue Items */}
        <div className="space-y-4 max-h-[60vh] overflow-y-auto">
          {queueItems.length === 0 ? (
            <div className="text-center py-12">
              <Download className={`w-16 h-16 ${theme.textSecondary} mx-auto mb-4 opacity-50`} />
              <p className={`${theme.textSecondary} text-lg`}>No downloads in queue</p>
              <p className={`${theme.textSecondary} text-sm mt-2`}>
                Start downloading tracks to see them here
              </p>
            </div>
          ) : (
            queueItems.map((item) => (
              <div key={item.id} className="relative">
                <div className="flex items-start justify-between mb-2">
                  <div className="flex-1">
                    <p className={`${theme.text} text-sm font-medium truncate`}>
                      {item.url}
                    </p>
                    <p className={`${theme.textSecondary} text-xs`}>
                      Added: {item.addedAt.toLocaleTimeString()}
                    </p>
                  </div>
                  <Button
                    onClick={() => removeFromQueue(item.id)}
                    variant="ghost"
                    size="sm"
                    className="p-1 ml-2"
                  >
                    <Trash2 className="w-4 h-4 text-red-400" />
                  </Button>
                </div>
                
                <DownloadProgress
                  jobId={item.jobId}
                  onComplete={(job) => handleJobComplete(item.id, job)}
                  onError={(error) => handleJobError(item.id, error)}
                />
              </div>
            ))
          )}
        </div>
      </Card>
    </div>
  );

  // Expose addToQueue method for parent components
  React.useImperativeHandle(React.createRef(), () => ({
    addToQueue
  }));
};
