import sqlite3 from 'sqlite3';
import { promisify } from 'util';
import path from 'path';
import fs from 'fs';

// Database interfaces
export interface DownloadJob {
  id: string;
  url: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  track_id?: string;
  track_title?: string;
  track_artist?: string;
  track_duration?: number;
  track_artwork_url?: string;
  file_path?: string;
  file_size?: number;
  error_message?: string;
  created_at: string;
  updated_at: string;
}

export interface DownloadedFile {
  id: string;
  job_id: string;
  original_filename: string;
  file_path: string;
  file_size: number;
  mime_type: string;
  created_at: string;
}

class Database {
  private db: sqlite3.Database | null = null;
  private dbPath: string;

  constructor() {
    // Create data directory if it doesn't exist
    const dataDir = path.join(process.cwd(), 'data');
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }
    
    this.dbPath = path.join(dataDir, 'soundcloud_downloader.db');
  }

  async connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.db = new sqlite3.Database(this.dbPath, (err) => {
        if (err) {
          console.error('Error opening database:', err);
          reject(err);
        } else {
          console.log('Connected to SQLite database');
          this.initializeTables().then(resolve).catch(reject);
        }
      });
    });
  }

  private async initializeTables(): Promise<void> {
    if (!this.db) throw new Error('Database not connected');

    const runAsync = promisify(this.db.run.bind(this.db));

    // Create download_jobs table
    await runAsync(`
      CREATE TABLE IF NOT EXISTS download_jobs (
        id TEXT PRIMARY KEY,
        url TEXT NOT NULL,
        status TEXT NOT NULL DEFAULT 'pending',
        progress INTEGER DEFAULT 0,
        track_id TEXT,
        track_title TEXT,
        track_artist TEXT,
        track_duration INTEGER,
        track_artwork_url TEXT,
        file_path TEXT,
        file_size INTEGER,
        error_message TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create downloaded_files table
    await runAsync(`
      CREATE TABLE IF NOT EXISTS downloaded_files (
        id TEXT PRIMARY KEY,
        job_id TEXT NOT NULL,
        original_filename TEXT NOT NULL,
        file_path TEXT NOT NULL,
        file_size INTEGER NOT NULL,
        mime_type TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (job_id) REFERENCES download_jobs (id)
      )
    `);

    // Create indexes
    await runAsync(`CREATE INDEX IF NOT EXISTS idx_jobs_status ON download_jobs(status)`);
    await runAsync(`CREATE INDEX IF NOT EXISTS idx_jobs_created_at ON download_jobs(created_at)`);
    await runAsync(`CREATE INDEX IF NOT EXISTS idx_files_job_id ON downloaded_files(job_id)`);

    console.log('Database tables initialized');
  }

  async createJob(job: Omit<DownloadJob, 'created_at' | 'updated_at'>): Promise<void> {
    if (!this.db) throw new Error('Database not connected');

    const runAsync = promisify(this.db.run.bind(this.db));
    
    await runAsync(`
      INSERT INTO download_jobs (
        id, url, status, progress, track_id, track_title, track_artist,
        track_duration, track_artwork_url, file_path, file_size, error_message
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      job.id, job.url, job.status, job.progress, job.track_id, job.track_title,
      job.track_artist, job.track_duration, job.track_artwork_url, job.file_path,
      job.file_size, job.error_message
    ]);
  }

  async updateJob(id: string, updates: Partial<DownloadJob>): Promise<void> {
    if (!this.db) throw new Error('Database not connected');

    const runAsync = promisify(this.db.run.bind(this.db));
    
    const fields = Object.keys(updates).filter(key => key !== 'id');
    const setClause = fields.map(field => `${field} = ?`).join(', ');
    const values = fields.map(field => (updates as any)[field]);
    
    await runAsync(`
      UPDATE download_jobs 
      SET ${setClause}, updated_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `, [...values, id]);
  }

  async getJob(id: string): Promise<DownloadJob | null> {
    if (!this.db) throw new Error('Database not connected');

    const getAsync = promisify(this.db.get.bind(this.db));
    
    const row = await getAsync(`
      SELECT * FROM download_jobs WHERE id = ?
    `, [id]) as DownloadJob | undefined;
    
    return row || null;
  }

  async getAllJobs(limit: number = 100): Promise<DownloadJob[]> {
    if (!this.db) throw new Error('Database not connected');

    const allAsync = promisify(this.db.all.bind(this.db));
    
    const rows = await allAsync(`
      SELECT * FROM download_jobs 
      ORDER BY created_at DESC 
      LIMIT ?
    `, [limit]) as DownloadJob[];
    
    return rows;
  }

  async saveFile(file: Omit<DownloadedFile, 'created_at'>): Promise<void> {
    if (!this.db) throw new Error('Database not connected');

    const runAsync = promisify(this.db.run.bind(this.db));
    
    await runAsync(`
      INSERT INTO downloaded_files (
        id, job_id, original_filename, file_path, file_size, mime_type
      ) VALUES (?, ?, ?, ?, ?, ?)
    `, [
      file.id, file.job_id, file.original_filename, 
      file.file_path, file.file_size, file.mime_type
    ]);
  }

  async getFile(id: string): Promise<DownloadedFile | null> {
    if (!this.db) throw new Error('Database not connected');

    const getAsync = promisify(this.db.get.bind(this.db));
    
    const row = await getAsync(`
      SELECT * FROM downloaded_files WHERE id = ?
    `, [id]) as DownloadedFile | undefined;
    
    return row || null;
  }

  async getFileByJobId(jobId: string): Promise<DownloadedFile | null> {
    if (!this.db) throw new Error('Database not connected');

    const getAsync = promisify(this.db.get.bind(this.db));
    
    const row = await getAsync(`
      SELECT * FROM downloaded_files WHERE job_id = ?
    `, [jobId]) as DownloadedFile | undefined;
    
    return row || null;
  }

  async cleanup(olderThanHours: number = 24): Promise<void> {
    if (!this.db) throw new Error('Database not connected');

    const runAsync = promisify(this.db.run.bind(this.db));
    
    // Delete old completed jobs and their files
    await runAsync(`
      DELETE FROM downloaded_files 
      WHERE job_id IN (
        SELECT id FROM download_jobs 
        WHERE status IN ('completed', 'failed') 
        AND created_at < datetime('now', '-${olderThanHours} hours')
      )
    `);
    
    await runAsync(`
      DELETE FROM download_jobs 
      WHERE status IN ('completed', 'failed') 
      AND created_at < datetime('now', '-${olderThanHours} hours')
    `);
  }

  async close(): Promise<void> {
    if (!this.db) return;
    
    return new Promise((resolve) => {
      this.db!.close((err) => {
        if (err) {
          console.error('Error closing database:', err);
        } else {
          console.log('Database connection closed');
        }
        resolve();
      });
    });
  }
}

// Singleton instance
let dbInstance: Database | null = null;

export async function getDatabase(): Promise<Database> {
  if (!dbInstance) {
    dbInstance = new Database();
    await dbInstance.connect();
  }
  return dbInstance;
}

export { Database };
