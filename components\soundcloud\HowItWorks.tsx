'use client';

import React from 'react';
import { howItWorksSteps } from '@/lib/constants';
import { useTheme } from './ThemeProvider';
import { Card } from '@/components/ui/Card';

export const HowItWorks: React.FC = () => {
  const { theme } = useTheme();

  return (
    <Card padding="xl">
      <h2 className={`text-3xl sm:text-4xl font-bold ${theme.text} mb-12 text-center`}>
        How It Works
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 sm:gap-12">
        {howItWorksSteps.map((item, index) => (
          <div key={index} className="text-center group">
            <div className={`relative mx-auto mb-6 w-24 h-24 bg-gradient-to-r ${theme.accent} rounded-3xl flex items-center justify-center shadow-lg group-hover:shadow-2xl transition-all duration-300 transform group-hover:scale-110`}>
              <item.icon className="w-10 h-10 text-white" />
              <div className={`absolute -top-2 -right-2 bg-gradient-to-r from-white to-gray-200 text-gray-800 w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold`}>
                {item.step}
              </div>
            </div>
            <h3 className={`text-xl font-bold ${theme.text} mb-4`}>{item.title}</h3>
            <p className={`${theme.textSecondary} leading-relaxed`}>{item.description}</p>
          </div>
        ))}
      </div>
    </Card>
  );
};
