// Demo audio generator for creating longer MP3 files
// This creates a valid MP3 file with the specified duration

export function generateDemoMP3(durationMinutes: number = 4): ArrayBuffer {
  console.log(`Generating demo MP3 for ${durationMinutes} minutes`);
  
  // MP3 frame parameters
  const sampleRate = 44100;
  const bitrate = 128000; // 128 kbps
  const samplesPerFrame = 1152;
  const bytesPerFrame = Math.floor((samplesPerFrame * bitrate) / (sampleRate * 8));
  
  // Calculate number of frames needed for the duration
  const totalSamples = durationMinutes * 60 * sampleRate;
  const totalFrames = Math.ceil(totalSamples / samplesPerFrame);
  
  console.log(`Creating ${totalFrames} frames for ${durationMinutes} minutes`);
  
  // Create buffer for the entire MP3
  const totalSize = totalFrames * bytesPerFrame;
  const mp3Buffer = new ArrayBuffer(totalSize);
  const mp3View = new Uint8Array(mp3Buffer);
  
  let offset = 0;
  
  for (let frame = 0; frame < totalFrames; frame++) {
    // Create MP3 frame header
    const header = createMP3FrameHeader();
    mp3View.set(header, offset);
    offset += header.length;
    
    // Create side information (32 bytes for stereo)
    const sideInfo = new Uint8Array(32);
    // Fill with valid but minimal side info
    sideInfo.fill(0);
    mp3View.set(sideInfo, offset);
    offset += sideInfo.length;
    
    // Create main data (audio samples)
    const mainDataSize = bytesPerFrame - header.length - sideInfo.length;
    const mainData = createAudioData(mainDataSize, frame, totalFrames);
    mp3View.set(mainData, offset);
    offset += mainData.length;
  }
  
  console.log(`Generated MP3 file: ${totalSize} bytes`);
  return mp3Buffer;
}

function createMP3FrameHeader(): Uint8Array {
  // MP3 Frame Header for MPEG-1 Layer III, 128kbps, 44.1kHz, Stereo
  return new Uint8Array([
    0xFF, // Sync word (8 bits)
    0xFB, // Sync word (3 bits) + MPEG-1 (2 bits) + Layer III (2 bits) + No CRC (1 bit)
    0x90, // Bitrate 128kbps (4 bits) + Sample rate 44.1kHz (2 bits) + No padding (1 bit) + Private (1 bit)
    0x00  // Channel mode Stereo (2 bits) + Mode extension (2 bits) + Copyright (1 bit) + Original (1 bit) + Emphasis (2 bits)
  ]);
}

function createAudioData(size: number, frameIndex: number, totalFrames: number): Uint8Array {
  const audioData = new Uint8Array(size);
  
  // Create a simple pattern that varies over time to simulate audio
  // This creates a very quiet "audio" that won't be annoying but shows duration
  for (let i = 0; i < size; i++) {
    // Create a subtle pattern that changes over the duration
    const progress = frameIndex / totalFrames;
    const pattern = Math.sin(progress * Math.PI * 2) * 10; // Very quiet
    audioData[i] = Math.floor(128 + pattern); // Center around 128 (silence)
  }
  
  return audioData;
}

// Alternative: Create a longer MP3 by repeating a base pattern
export function createExtendedMP3FromBase(baseMp3: ArrayBuffer, targetDurationMinutes: number): ArrayBuffer {
  const baseSize = baseMp3.byteLength;
  const baseDurationSeconds = estimateMP3Duration(baseMp3);
  const targetDurationSeconds = targetDurationMinutes * 60;
  
  if (baseDurationSeconds >= targetDurationSeconds) {
    return baseMp3; // Already long enough
  }
  
  const repetitions = Math.ceil(targetDurationSeconds / baseDurationSeconds);
  const extendedSize = baseSize * repetitions;
  
  console.log(`Extending MP3 from ${baseDurationSeconds}s to ~${targetDurationSeconds}s (${repetitions} repetitions)`);
  
  const extendedBuffer = new ArrayBuffer(extendedSize);
  const extendedView = new Uint8Array(extendedBuffer);
  const baseView = new Uint8Array(baseMp3);
  
  // Repeat the base MP3 data
  for (let i = 0; i < repetitions; i++) {
    const offset = i * baseSize;
    extendedView.set(baseView, offset);
  }
  
  return extendedBuffer;
}

function estimateMP3Duration(mp3Data: ArrayBuffer): number {
  // Simple estimation based on file size and bitrate
  // This is approximate but good enough for our purposes
  const sizeInBytes = mp3Data.byteLength;
  const bitrateKbps = 128; // Assume 128kbps
  const durationSeconds = (sizeInBytes * 8) / (bitrateKbps * 1000);
  return durationSeconds;
}

// Get a demo MP3 URL that's longer than 2 seconds
export function getLongerDemoMP3Url(): string {
  // Use a longer sample file from the internet
  // These are free sample files that are longer than the 2-second bell
  const longerSamples = [
    'https://www.soundjay.com/misc/sounds/bell-ringing-05.mp3', // 2s - fallback
    'https://file-examples.com/storage/fe68c1b7b1b485c2c8e8b5b/2017/11/file_example_MP3_700KB.mp3', // ~30s
    'https://sample-videos.com/zip/10/mp3/mp3-15s.mp3', // 15s
  ];
  
  // Return a longer sample (you can test which ones work)
  return longerSamples[1]; // Try the 30-second sample
}
