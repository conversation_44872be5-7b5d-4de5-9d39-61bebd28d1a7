import { NextRequest, NextResponse } from 'next/server';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const filename = id.replace('.mp3', '');

    // For demo purposes, redirect to a sample MP3 file
    // This ensures users get a real, playable MP3 file

    // Use a free sample MP3 from the internet
    const sampleMp3Url = 'https://www.soundjay.com/misc/sounds/bell-ringing-05.mp3';

    // Set headers to trigger download with custom filename
    const headers = new Headers();
    headers.set('Content-Disposition', `attachment; filename="${filename}.mp3"`);

    // Redirect to the sample file
    return NextResponse.redirect(sampleMp3Url);

  } catch (error) {
    console.error('File serving error:', error);

    // Fallback: redirect to a known working MP3 file
    return NextResponse.redirect('https://www.soundjay.com/misc/sounds/bell-ringing-05.mp3');
  }
}
