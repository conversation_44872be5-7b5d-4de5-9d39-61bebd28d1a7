import { NextRequest, NextResponse } from 'next/server';
import { getDatabase } from '@/lib/database';
import { getFileStorage } from '@/lib/file-storage';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const jobId = id.replace('.mp3', '');

    console.log('Serving file for job:', jobId);

    const db = await getDatabase();
    const storage = getFileStorage();

    // Get job info from database
    const job = await db.getJob(jobId);
    if (!job || job.status !== 'completed') {
      return NextResponse.json({
        success: false,
        error: 'File not ready or job not found'
      }, { status: 404 });
    }

    // Get file info from database
    const fileRecord = await db.getFileByJobId(jobId);
    if (!fileRecord) {
      return NextResponse.json({
        success: false,
        error: 'File record not found'
      }, { status: 404 });
    }

    // Read file from storage
    const fileResult = await storage.getFile(fileRecord.id);
    if (!fileResult.exists) {
      return NextResponse.json({
        success: false,
        error: 'File not found in storage'
      }, { status: 404 });
    }

    console.log('Serving downloaded file, size:', fileResult.data.length);

    // Set proper headers for download
    const headers = new Headers();
    headers.set('Content-Type', 'audio/mpeg');
    headers.set('Content-Disposition', `attachment; filename="${fileRecord.original_filename}"`);
    headers.set('Content-Length', fileResult.data.length.toString());
    headers.set('Cache-Control', 'no-cache');
    headers.set('Accept-Ranges', 'bytes');

    return new NextResponse(fileResult.data, {
      status: 200,
      headers
    });

  } catch (error) {
    console.error('File serving error:', error);

    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}


