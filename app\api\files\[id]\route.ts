import { NextRequest, NextResponse } from 'next/server';

// Import the same storage from download route
// In production, this would be a shared database/Redis
declare global {
  var downloadedFiles: Map<string, ArrayBuffer>;
}

// Initialize global storage if not exists
if (!global.downloadedFiles) {
  global.downloadedFiles = new Map<string, ArrayBuffer>();
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const fileId = id.replace('.mp3', '');

    console.log('Serving file:', fileId);

    // Check if we have the downloaded file in memory
    const audioData = global.downloadedFiles.get(fileId);

    if (audioData) {
      console.log('Serving downloaded file, size:', audioData.byteLength);

      // Set proper headers for download
      const headers = new Headers();
      headers.set('Content-Type', 'audio/mpeg');
      headers.set('Content-Disposition', `attachment; filename="${fileId}.mp3"`);
      headers.set('Content-Length', audioData.byteLength.toString());
      headers.set('Cache-Control', 'no-cache');
      headers.set('Accept-Ranges', 'bytes');

      return new NextResponse(audioData, {
        status: 200,
        headers
      });
    }

    // Fallback: fetch a sample MP3 file
    console.log('File not found in storage, using fallback');
    const sampleMp3Url = 'https://www.soundjay.com/misc/sounds/bell-ringing-05.mp3';

    const response = await fetch(sampleMp3Url);
    if (!response.ok) {
      throw new Error('Failed to fetch fallback MP3');
    }

    const mp3Data = await response.arrayBuffer();

    const headers = new Headers();
    headers.set('Content-Type', 'audio/mpeg');
    headers.set('Content-Disposition', `attachment; filename="${fileId}.mp3"`);
    headers.set('Content-Length', mp3Data.byteLength.toString());
    headers.set('Cache-Control', 'no-cache');

    return new NextResponse(mp3Data, {
      status: 200,
      headers
    });

  } catch (error) {
    console.error('File serving error:', error);

    return NextResponse.json({
      success: false,
      error: 'File not found'
    }, { status: 404 });
  }
}


