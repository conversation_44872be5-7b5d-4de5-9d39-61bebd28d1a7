'use client';

import React, { useState } from 'react';
import { Track } from '@/types/soundcloud';
import { mockTracks } from '@/lib/constants';
import { ThemeProvider, useTheme } from '@/components/soundcloud/ThemeProvider';
import { FloatingParticles } from '@/components/soundcloud/FloatingParticles';
import { Header } from '@/components/layout/Header';
import { Footer } from '@/components/layout/Footer';
import { SearchForm } from '@/components/soundcloud/SearchForm';
import { SearchResults } from '@/components/soundcloud/SearchResults';
import { FeatureGrid } from '@/components/soundcloud/FeatureGrid';
import { HowItWorks } from '@/components/soundcloud/HowItWorks';

const SoundCloudContent: React.FC = () => {
  const [searchResults, setSearchResults] = useState<Track[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { theme, isClient } = useTheme();

  const handleSearch = (url: string) => {
    if (!url.trim()) return;
    setIsLoading(true);

    // Simulate API call
    setTimeout(() => {
      setSearchResults(mockTracks);
      setIsLoading(false);
    }, 2000);
  };

  const handleDownload = (track: Track) => {
    console.log('Download UI triggered for:', track.title);
    // Here you would implement the actual download logic
  };

  return (
    <div className={`min-h-screen bg-gradient-to-br ${theme.bg} transition-all duration-700 relative overflow-hidden`}>
      <FloatingParticles />

      {/* Animated Background Elements - Only render on client */}
      {isClient && (
        <div className="fixed inset-0 opacity-10">
          <div className={`absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r ${theme.accent} rounded-full blur-3xl animate-pulse`}></div>
          <div className={`absolute bottom-1/4 right-1/4 w-64 h-64 bg-gradient-to-r ${theme.accent} rounded-full blur-3xl animate-pulse`} style={{animationDelay: '2s'}}></div>
        </div>
      )}

      <Header />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <h2 className={`text-4xl sm:text-6xl font-bold ${theme.text} mb-4 animate-fade-in`}>
            Transform Your
            <span className={`bg-gradient-to-r ${theme.accent} bg-clip-text text-transparent ml-3`}>
              Sound Experience
            </span>
          </h2>
          <p className={`text-lg sm:text-xl ${theme.textSecondary} max-w-2xl mx-auto`}>
            Convert and download your favorite tracks with cutting-edge technology
          </p>
        </div>

        {/* Main Download Section */}
        <SearchForm onSearch={handleSearch} isLoading={isLoading} />

        {/* Search Results */}
        <SearchResults tracks={searchResults} onDownload={handleDownload} />

        {/* Features Grid */}
        <FeatureGrid />

        {/* How It Works */}
        <HowItWorks />
      </div>

      <Footer />
    </div>
  );
};

export default function SoundCloudPage() {
  return (
    <ThemeProvider>
      <SoundCloudContent />
    </ThemeProvider>
  );
}
